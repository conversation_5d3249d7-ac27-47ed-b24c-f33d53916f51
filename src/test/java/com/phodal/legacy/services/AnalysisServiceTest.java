package com.phodal.legacy.services;

import com.phodal.legacy.model.ComponentModel;
import com.phodal.legacy.model.DependencyGraph;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Test class for AnalysisService to verify comprehensive project analysis capabilities.
 */
class AnalysisServiceTest {
    
    private AnalysisService analysisService;
    
    @TempDir
    Path tempDir;
    
    @BeforeEach
    void setUp() {
        analysisService = new AnalysisService();
    }
    
    @Test
    void testAnalyzeEmptyProject() throws IOException {
        AnalysisService.AnalysisOptions options = new AnalysisService.AnalysisOptions();
        
        AnalysisService.AnalysisResult result = analysisService.analyzeProject(tempDir, options);
        
        assertTrue(result.isSuccess());
        assertNull(result.getErrorMessage());
        assertTrue(result.getDuration() >= 0);
        
        // Should have empty component lists
        assertTrue(result.getJspComponents().isEmpty());
        assertTrue(result.getJavaComponents().isEmpty());
        assertTrue(result.getWebXmlComponents().isEmpty());
        
        // Should have empty dependency graph
        DependencyGraph graph = result.getDependencyGraph();
        assertNotNull(graph);
        assertTrue(graph.getAllComponents().isEmpty());
        
        // Should have summary
        AnalysisService.AnalysisSummary summary = result.getSummary();
        assertNotNull(summary);
        assertTrue(summary.getComponentCounts().isEmpty());
    }
    
    @Test
    void testAnalyzeSimpleProject() throws IOException {
        // Create a simple project structure
        createSimpleProject();
        
        AnalysisService.AnalysisOptions options = new AnalysisService.AnalysisOptions();
        
        AnalysisService.AnalysisResult result = analysisService.analyzeProject(tempDir, options);
        
        assertTrue(result.isSuccess());
        assertNull(result.getErrorMessage());
        
        // Should find components
        assertEquals(1, result.getJspComponents().size());
        assertEquals(1, result.getJavaComponents().size());
        assertEquals(1, result.getWebXmlComponents().size());
        
        // Check JSP component
        ComponentModel jspComponent = result.getJspComponents().get(0);
        assertEquals(ComponentModel.ComponentType.JSP_PAGE, jspComponent.getType());
        assertTrue(jspComponent.getName().endsWith(".jsp"));
        
        // Check Java component
        ComponentModel javaComponent = result.getJavaComponents().get(0);
        assertEquals(ComponentModel.ComponentType.SERVLET, javaComponent.getType());
        assertTrue(javaComponent.getName().endsWith(".java"));
        
        // Check web.xml component
        ComponentModel webXmlComponent = result.getWebXmlComponents().get(0);
        assertEquals(ComponentModel.ComponentType.WEB_XML, webXmlComponent.getType());
        assertEquals("web.xml", webXmlComponent.getName());
        
        // Check dependency graph
        DependencyGraph graph = result.getDependencyGraph();
        assertNotNull(graph);
        assertEquals(3, graph.getAllComponents().size());
        
        // Check summary
        AnalysisService.AnalysisSummary summary = result.getSummary();
        assertNotNull(summary);
        Map<ComponentModel.ComponentType, Integer> counts = summary.getComponentCounts();
        assertEquals(Integer.valueOf(1), counts.get(ComponentModel.ComponentType.JSP_PAGE));
        assertEquals(Integer.valueOf(1), counts.get(ComponentModel.ComponentType.SERVLET));
        assertEquals(Integer.valueOf(1), counts.get(ComponentModel.ComponentType.WEB_XML));
        
        assertTrue(summary.getMigrationComplexity() > 0);
        assertNotNull(summary.getMigrationOrder());
        assertEquals(3, summary.getMigrationOrder().size());
    }
    
    @Test
    void testAnalyzeWithSelectiveOptions() throws IOException {
        createSimpleProject();
        
        // Test with only JSP analysis enabled
        AnalysisService.AnalysisOptions jspOnlyOptions = new AnalysisService.AnalysisOptions();
        jspOnlyOptions.setIncludeJsp(true);
        jspOnlyOptions.setIncludeJava(false);
        jspOnlyOptions.setIncludeWebXml(false);
        
        AnalysisService.AnalysisResult result = analysisService.analyzeProject(tempDir, jspOnlyOptions);
        
        assertTrue(result.isSuccess());
        assertEquals(1, result.getJspComponents().size());
        assertTrue(result.getJavaComponents().isEmpty());
        assertTrue(result.getWebXmlComponents().isEmpty());
        
        // Test with only Java analysis enabled
        AnalysisService.AnalysisOptions javaOnlyOptions = new AnalysisService.AnalysisOptions();
        javaOnlyOptions.setIncludeJsp(false);
        javaOnlyOptions.setIncludeJava(true);
        javaOnlyOptions.setIncludeWebXml(false);
        
        result = analysisService.analyzeProject(tempDir, javaOnlyOptions);
        
        assertTrue(result.isSuccess());
        assertTrue(result.getJspComponents().isEmpty());
        assertEquals(1, result.getJavaComponents().size());
        assertTrue(result.getWebXmlComponents().isEmpty());
    }
    
    @Test
    void testAnalyzeComplexProject() throws IOException {
        // Create a more complex project structure
        createComplexProject();
        
        AnalysisService.AnalysisOptions options = new AnalysisService.AnalysisOptions();
        
        AnalysisService.AnalysisResult result = analysisService.analyzeProject(tempDir, options);
        
        assertTrue(result.isSuccess());
        
        // Should find multiple components
        assertTrue(result.getJspComponents().size() >= 2);
        assertTrue(result.getJavaComponents().size() >= 3);
        assertEquals(1, result.getWebXmlComponents().size());
        
        // Check summary
        AnalysisService.AnalysisSummary summary = result.getSummary();
        assertNotNull(summary);
        
        Map<ComponentModel.ComponentType, Integer> counts = summary.getComponentCounts();
        assertTrue(counts.get(ComponentModel.ComponentType.JSP_PAGE) >= 2);
        assertTrue(counts.get(ComponentModel.ComponentType.SERVLET) >= 1);
        assertTrue(counts.get(ComponentModel.ComponentType.FILTER) >= 1);
        assertTrue(counts.get(ComponentModel.ComponentType.JAVA_CLASS) >= 1);
        
        // Migration complexity should be higher for complex project
        assertTrue(summary.getMigrationComplexity() > 100);
        
        // Should have migration order
        assertNotNull(summary.getMigrationOrder());
        assertFalse(summary.getMigrationOrder().isEmpty());
    }
    
    @Test
    void testAnalyzeNonExistentProject() {
        Path nonExistentPath = tempDir.resolve("non-existent");
        AnalysisService.AnalysisOptions options = new AnalysisService.AnalysisOptions();
        
        assertThrows(IllegalArgumentException.class, () -> {
            analysisService.analyzeProject(nonExistentPath, options);
        });
    }
    
    @Test
    void testAnalysisOptions() {
        AnalysisService.AnalysisOptions options = new AnalysisService.AnalysisOptions();
        
        // Test default values
        assertTrue(options.isIncludeJsp());
        assertTrue(options.isIncludeJava());
        assertFalse(options.isIncludeBytecode());
        assertTrue(options.isIncludeWebXml());
        assertTrue(options.isGenerateReport());
        assertEquals("detailed", options.getReportFormat());
        
        // Test setters
        options.setIncludeJsp(false);
        options.setIncludeJava(false);
        options.setIncludeBytecode(true);
        options.setIncludeWebXml(false);
        options.setGenerateReport(false);
        options.setReportFormat("json");
        
        assertFalse(options.isIncludeJsp());
        assertFalse(options.isIncludeJava());
        assertTrue(options.isIncludeBytecode());
        assertFalse(options.isIncludeWebXml());
        assertFalse(options.isGenerateReport());
        assertEquals("json", options.getReportFormat());
    }
    
    private void createSimpleProject() throws IOException {
        // Create directory structure
        Path webInfDir = tempDir.resolve("WEB-INF");
        Path srcDir = tempDir.resolve("src/main/java/com/example");
        Files.createDirectories(webInfDir);
        Files.createDirectories(srcDir);
        
        // Create JSP file
        String jspContent = """
            <%@ page language="java" contentType="text/html; charset=UTF-8" %>
            <html>
            <body>
                <h1>Test JSP</h1>
                <% String message = "Hello World"; %>
                <p><%= message %></p>
            </body>
            </html>
            """;
        Files.writeString(tempDir.resolve("index.jsp"), jspContent);
        
        // Create Java servlet
        String servletContent = """
            package com.example;
            
            import javax.servlet.http.HttpServlet;
            import javax.servlet.http.HttpServletRequest;
            import javax.servlet.http.HttpServletResponse;
            import javax.servlet.annotation.WebServlet;
            import java.io.IOException;
            
            @WebServlet("/test")
            public class TestServlet extends HttpServlet {
                protected void doGet(HttpServletRequest request, HttpServletResponse response) 
                        throws IOException {
                    response.getWriter().println("Hello from servlet");
                }
            }
            """;
        Files.writeString(srcDir.resolve("TestServlet.java"), servletContent);
        
        // Create web.xml
        String webXmlContent = """
            <?xml version="1.0" encoding="UTF-8"?>
            <web-app version="3.0">
                <display-name>Test Application</display-name>
                <servlet>
                    <servlet-name>TestServlet</servlet-name>
                    <servlet-class>com.example.TestServlet</servlet-class>
                </servlet>
                <servlet-mapping>
                    <servlet-name>TestServlet</servlet-name>
                    <url-pattern>/test</url-pattern>
                </servlet-mapping>
            </web-app>
            """;
        Files.writeString(webInfDir.resolve("web.xml"), webXmlContent);
    }
    
    private void createComplexProject() throws IOException {
        // Create directory structure
        Path webInfDir = tempDir.resolve("WEB-INF");
        Path srcDir = tempDir.resolve("src/main/java/com/example");
        Files.createDirectories(webInfDir);
        Files.createDirectories(srcDir);
        
        // Create multiple JSP files
        String jsp1Content = """
            <%@ page language="java" import="java.util.Date" %>
            <html><body><h1>Page 1</h1><%= new Date() %></body></html>
            """;
        String jsp2Content = """
            <%@ page language="java" %>
            <%@ include file="header.jsp" %>
            <html><body><h1>Page 2</h1></body></html>
            """;
        Files.writeString(tempDir.resolve("page1.jsp"), jsp1Content);
        Files.writeString(tempDir.resolve("page2.jsp"), jsp2Content);
        
        // Create servlet
        String servletContent = """
            package com.example;
            import javax.servlet.http.HttpServlet;
            import javax.servlet.annotation.WebServlet;
            @WebServlet("/test")
            public class TestServlet extends HttpServlet {}
            """;
        Files.writeString(srcDir.resolve("TestServlet.java"), servletContent);
        
        // Create filter
        String filterContent = """
            package com.example;
            import javax.servlet.Filter;
            import javax.servlet.annotation.WebFilter;
            @WebFilter("/*")
            public class TestFilter implements Filter {}
            """;
        Files.writeString(srcDir.resolve("TestFilter.java"), filterContent);
        
        // Create regular Java class
        String javaClassContent = """
            package com.example;
            public class TestClass {
                public void method1() {}
                public void method2() {}
                public void method3() {}
            }
            """;
        Files.writeString(srcDir.resolve("TestClass.java"), javaClassContent);
        
        // Create complex web.xml
        String webXmlContent = """
            <?xml version="1.0" encoding="UTF-8"?>
            <web-app version="3.0">
                <display-name>Complex Application</display-name>
                <servlet>
                    <servlet-name>TestServlet</servlet-name>
                    <servlet-class>com.example.TestServlet</servlet-class>
                </servlet>
                <servlet-mapping>
                    <servlet-name>TestServlet</servlet-name>
                    <url-pattern>/test</url-pattern>
                </servlet-mapping>
                <filter>
                    <filter-name>TestFilter</filter-name>
                    <filter-class>com.example.TestFilter</filter-class>
                </filter>
                <filter-mapping>
                    <filter-name>TestFilter</filter-name>
                    <url-pattern>/*</url-pattern>
                </filter-mapping>
            </web-app>
            """;
        Files.writeString(webInfDir.resolve("web.xml"), webXmlContent);
    }
}
