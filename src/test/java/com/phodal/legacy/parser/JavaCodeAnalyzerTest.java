package com.phodal.legacy.parser;

import com.phodal.legacy.model.ComponentModel;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Test class for JavaCodeAnalyzer to verify Java source code parsing and analysis capabilities.
 */
class JavaCodeAnalyzerTest {
    
    private JavaCodeAnalyzer javaAnalyzer;
    
    @TempDir
    Path tempDir;
    
    @BeforeEach
    void setUp() {
        javaAnalyzer = new JavaCodeAnalyzer();
    }
    
    @Test
    void testAnalyzeSimpleJavaClass() throws IOException {
        String javaContent = """
            package com.example;
            
            import java.util.List;
            import java.util.ArrayList;
            
            public class SimpleClass {
                private String name;
                
                public SimpleClass(String name) {
                    this.name = name;
                }
                
                public String getName() {
                    return name;
                }
                
                public void setName(String name) {
                    this.name = name;
                }
            }
            """;
        
        Path javaFile = tempDir.resolve("SimpleClass.java");
        Files.writeString(javaFile, javaContent);
        
        ComponentModel component = javaAnalyzer.analyzeJavaFile(javaFile, tempDir);
        
        assertNotNull(component);
        assertEquals("SimpleClass.java", component.getName());
        assertEquals(ComponentModel.ComponentType.JAVA_CLASS, component.getType());
        assertEquals(javaFile.toString(), component.getSourcePath());
        
        // Check metadata
        Map<String, Object> properties = component.getProperties();
        assertEquals("com.example", properties.get("packageName"));
        
        @SuppressWarnings("unchecked")
        List<String> classes = (List<String>) properties.get("classes");
        assertTrue(classes.contains("SimpleClass"));
        
        // Check dependencies
        assertTrue(component.getDependencies().contains("java.util.List"));
        assertTrue(component.getDependencies().contains("java.util.ArrayList"));
        
        // Check complexity (1 class + 1 constructor + 2 methods)
        Integer complexity = (Integer) properties.get("complexity");
        assertEquals(4, complexity);
    }
    
    @Test
    void testAnalyzeServletClass() throws IOException {
        String servletContent = """
            package com.example.servlet;
            
            import javax.servlet.http.HttpServlet;
            import javax.servlet.http.HttpServletRequest;
            import javax.servlet.http.HttpServletResponse;
            import javax.servlet.annotation.WebServlet;
            import java.io.IOException;
            
            @WebServlet("/test")
            public class TestServlet extends HttpServlet {
                
                @Override
                protected void doGet(HttpServletRequest request, HttpServletResponse response) 
                        throws IOException {
                    response.getWriter().println("Hello from servlet");
                }
                
                @Override
                protected void doPost(HttpServletRequest request, HttpServletResponse response) 
                        throws IOException {
                    doGet(request, response);
                }
            }
            """;
        
        Path servletFile = tempDir.resolve("TestServlet.java");
        Files.writeString(servletFile, servletContent);
        
        ComponentModel component = javaAnalyzer.analyzeJavaFile(servletFile, tempDir);
        
        assertNotNull(component);
        assertEquals(ComponentModel.ComponentType.SERVLET, component.getType());
        
        // Check annotations
        @SuppressWarnings("unchecked")
        List<String> annotations = (List<String>) component.getProperties().get("annotations");
        assertTrue(annotations.contains("WebServlet"));
        assertTrue(annotations.contains("Override"));
        
        // Check superclass
        @SuppressWarnings("unchecked")
        List<String> superClasses = (List<String>) component.getProperties().get("superClasses");
        assertTrue(superClasses.contains("HttpServlet"));
        
        // Check dependencies
        assertTrue(component.getDependencies().contains("javax.servlet.http.HttpServlet"));
        assertTrue(component.getDependencies().contains("javax.servlet.annotation.WebServlet"));
    }
    
    @Test
    void testAnalyzeFilterClass() throws IOException {
        String filterContent = """
            package com.example.filter;
            
            import javax.servlet.Filter;
            import javax.servlet.FilterChain;
            import javax.servlet.FilterConfig;
            import javax.servlet.ServletException;
            import javax.servlet.ServletRequest;
            import javax.servlet.ServletResponse;
            import javax.servlet.annotation.WebFilter;
            import java.io.IOException;
            
            @WebFilter("/*")
            public class LoggingFilter implements Filter {
                
                @Override
                public void init(FilterConfig filterConfig) throws ServletException {
                    // Initialization code
                }
                
                @Override
                public void doFilter(ServletRequest request, ServletResponse response, 
                                   FilterChain chain) throws IOException, ServletException {
                    System.out.println("Request received");
                    chain.doFilter(request, response);
                    System.out.println("Response sent");
                }
                
                @Override
                public void destroy() {
                    // Cleanup code
                }
            }
            """;
        
        Path filterFile = tempDir.resolve("LoggingFilter.java");
        Files.writeString(filterFile, filterContent);
        
        ComponentModel component = javaAnalyzer.analyzeJavaFile(filterFile, tempDir);
        
        assertNotNull(component);
        assertEquals(ComponentModel.ComponentType.FILTER, component.getType());
        
        // Check implemented interfaces
        @SuppressWarnings("unchecked")
        List<String> interfaces = (List<String>) component.getProperties().get("implementedInterfaces");
        assertTrue(interfaces.contains("Filter"));
        
        // Check annotations
        @SuppressWarnings("unchecked")
        List<String> annotations = (List<String>) component.getProperties().get("annotations");
        assertTrue(annotations.contains("WebFilter"));
    }
    
    @Test
    void testAnalyzeListenerClass() throws IOException {
        String listenerContent = """
            package com.example.listener;
            
            import javax.servlet.ServletContextEvent;
            import javax.servlet.ServletContextListener;
            import javax.servlet.annotation.WebListener;
            
            @WebListener
            public class AppContextListener implements ServletContextListener {
                
                @Override
                public void contextInitialized(ServletContextEvent sce) {
                    System.out.println("Application started");
                }
                
                @Override
                public void contextDestroyed(ServletContextEvent sce) {
                    System.out.println("Application stopped");
                }
            }
            """;
        
        Path listenerFile = tempDir.resolve("AppContextListener.java");
        Files.writeString(listenerFile, listenerContent);
        
        ComponentModel component = javaAnalyzer.analyzeJavaFile(listenerFile, tempDir);
        
        assertNotNull(component);
        assertEquals(ComponentModel.ComponentType.LISTENER, component.getType());
        
        // Check implemented interfaces
        @SuppressWarnings("unchecked")
        List<String> interfaces = (List<String>) component.getProperties().get("implementedInterfaces");
        assertTrue(interfaces.contains("ServletContextListener"));
    }
    
    @Test
    void testAnalyzeInterfaceAndEnum() throws IOException {
        String javaContent = """
            package com.example;
            
            public interface UserService {
                void saveUser(User user);
                User findUser(String id);
            }
            
            enum UserStatus {
                ACTIVE, INACTIVE, PENDING
            }
            
            class User {
                private String id;
                private UserStatus status;
            }
            """;
        
        Path javaFile = tempDir.resolve("UserService.java");
        Files.writeString(javaFile, javaContent);
        
        ComponentModel component = javaAnalyzer.analyzeJavaFile(javaFile, tempDir);
        
        assertNotNull(component);
        
        // Check interfaces
        @SuppressWarnings("unchecked")
        List<String> interfaces = (List<String>) component.getProperties().get("interfaces");
        assertTrue(interfaces.contains("UserService"));
        
        // Check enums
        @SuppressWarnings("unchecked")
        List<String> enums = (List<String>) component.getProperties().get("enums");
        assertTrue(enums.contains("UserStatus"));
        
        // Check classes
        @SuppressWarnings("unchecked")
        List<String> classes = (List<String>) component.getProperties().get("classes");
        assertTrue(classes.contains("User"));
    }
    
    @Test
    void testAnalyzeMultipleJavaFiles() throws IOException {
        // Create multiple Java files
        String class1Content = """
            package com.example;
            public class Class1 {
                public void method1() {}
            }
            """;
        
        String class2Content = """
            package com.example;
            import java.util.Date;
            public class Class2 {
                private Date timestamp;
                public Date getTimestamp() { return timestamp; }
            }
            """;
        
        Path class1File = tempDir.resolve("Class1.java");
        Path class2File = tempDir.resolve("Class2.java");
        Files.writeString(class1File, class1Content);
        Files.writeString(class2File, class2Content);
        
        List<ComponentModel> components = javaAnalyzer.analyzeJavaFiles(tempDir);
        
        assertEquals(2, components.size());
        
        // Verify both files were analyzed
        boolean foundClass1 = false, foundClass2 = false;
        for (ComponentModel component : components) {
            if ("Class1.java".equals(component.getName())) {
                foundClass1 = true;
                assertEquals(ComponentModel.ComponentType.JAVA_CLASS, component.getType());
            } else if ("Class2.java".equals(component.getName())) {
                foundClass2 = true;
                assertEquals(ComponentModel.ComponentType.JAVA_CLASS, component.getType());
                assertTrue(component.getDependencies().contains("java.util.Date"));
            }
        }
        
        assertTrue(foundClass1);
        assertTrue(foundClass2);
    }
    
    @Test
    void testAnalyzeInvalidJavaFile() throws IOException {
        String invalidContent = """
            package com.example;
            
            public class InvalidClass {
                // Missing closing brace
            """;
        
        Path invalidFile = tempDir.resolve("InvalidClass.java");
        Files.writeString(invalidFile, invalidContent);
        
        ComponentModel component = javaAnalyzer.analyzeJavaFile(invalidFile, tempDir);
        
        // Should return null for invalid Java files
        assertNull(component);
    }
    
    @Test
    void testAnalyzeEmptyDirectory() throws IOException {
        List<ComponentModel> components = javaAnalyzer.analyzeJavaFiles(tempDir);
        
        assertTrue(components.isEmpty());
    }
    
    @Test
    void testAnalyzeNonJavaFiles() throws IOException {
        // Create non-Java files
        Path txtFile = tempDir.resolve("test.txt");
        Path xmlFile = tempDir.resolve("test.xml");
        Files.writeString(txtFile, "Plain text");
        Files.writeString(xmlFile, "<xml>content</xml>");
        
        List<ComponentModel> components = javaAnalyzer.analyzeJavaFiles(tempDir);
        
        assertTrue(components.isEmpty());
    }
}
