package com.phodal.legacy.model;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Test class for DependencyGraph to verify graph construction and analysis capabilities.
 */
class DependencyGraphTest {
    
    private DependencyGraph graph;
    
    @BeforeEach
    void setUp() {
        graph = new DependencyGraph();
    }
    
    @Test
    void testAddSingleComponent() {
        ComponentModel component = new ComponentModel("comp1", "Component1", ComponentModel.ComponentType.JAVA_CLASS);
        
        graph.addComponent(component);
        
        assertEquals(1, graph.getAllComponents().size());
        assertEquals(1, graph.getAllNodes().size());
        
        GraphNode node = graph.getNode("comp1");
        assertNotNull(node);
        assertEquals(component, node.getComponent());
    }
    
    @Test
    void testAddMultipleComponents() {
        ComponentModel comp1 = new ComponentModel("comp1", "Component1", ComponentModel.ComponentType.JAVA_CLASS);
        ComponentModel comp2 = new ComponentModel("comp2", "Component2", ComponentModel.ComponentType.JSP_PAGE);
        ComponentModel comp3 = new ComponentModel("comp3", "Component3", ComponentModel.ComponentType.SERVLET);
        
        graph.addComponent(comp1);
        graph.addComponent(comp2);
        graph.addComponent(comp3);
        
        assertEquals(3, graph.getAllComponents().size());
        assertEquals(3, graph.getAllNodes().size());
    }
    
    @Test
    void testBuildSimpleDependencies() {
        ComponentModel comp1 = new ComponentModel("comp1", "Component1", ComponentModel.ComponentType.JAVA_CLASS);
        ComponentModel comp2 = new ComponentModel("comp2", "Component2", ComponentModel.ComponentType.JSP_PAGE);
        
        // comp2 depends on comp1
        comp2.addDependency("comp1");
        
        graph.addComponent(comp1);
        graph.addComponent(comp2);
        graph.buildDependencies();
        
        GraphNode node1 = graph.getNode("comp1");
        GraphNode node2 = graph.getNode("comp2");
        
        // Check dependencies
        assertTrue(node2.getDependencies().contains(node1));
        assertTrue(node1.getDependents().contains(node2));
        
        // Check root and leaf nodes
        assertTrue(node1.isRoot());
        assertFalse(node1.isLeaf());
        assertFalse(node2.isRoot());
        assertTrue(node2.isLeaf());
    }
    
    @Test
    void testGetNodesByType() {
        ComponentModel servlet = new ComponentModel("servlet1", "TestServlet", ComponentModel.ComponentType.SERVLET);
        ComponentModel jsp = new ComponentModel("jsp1", "test.jsp", ComponentModel.ComponentType.JSP_PAGE);
        ComponentModel javaClass = new ComponentModel("class1", "TestClass", ComponentModel.ComponentType.JAVA_CLASS);
        
        graph.addComponent(servlet);
        graph.addComponent(jsp);
        graph.addComponent(javaClass);
        
        List<GraphNode> servletNodes = graph.getNodesByType(ComponentModel.ComponentType.SERVLET);
        List<GraphNode> jspNodes = graph.getNodesByType(ComponentModel.ComponentType.JSP_PAGE);
        List<GraphNode> javaNodes = graph.getNodesByType(ComponentModel.ComponentType.JAVA_CLASS);
        
        assertEquals(1, servletNodes.size());
        assertEquals(1, jspNodes.size());
        assertEquals(1, javaNodes.size());
        
        assertEquals("servlet1", servletNodes.get(0).getId());
        assertEquals("jsp1", jspNodes.get(0).getId());
        assertEquals("class1", javaNodes.get(0).getId());
    }
    
    @Test
    void testTopologicalSort() {
        // Create a dependency chain: A -> B -> C
        ComponentModel compA = new ComponentModel("A", "ComponentA", ComponentModel.ComponentType.JAVA_CLASS);
        ComponentModel compB = new ComponentModel("B", "ComponentB", ComponentModel.ComponentType.JAVA_CLASS);
        ComponentModel compC = new ComponentModel("C", "ComponentC", ComponentModel.ComponentType.JSP_PAGE);

        compB.addDependency("A");
        compC.addDependency("B");

        graph.addComponent(compA);
        graph.addComponent(compB);
        graph.addComponent(compC);
        graph.buildDependencies();

        // Verify dependencies were added correctly
        GraphNode nodeA = graph.getNode("A");
        GraphNode nodeB = graph.getNode("B");
        GraphNode nodeC = graph.getNode("C");

        assertNotNull(nodeA);
        assertNotNull(nodeB);
        assertNotNull(nodeC);

        // Check that dependencies are correctly established
        assertTrue(nodeB.getDependencies().contains(nodeA), "B should depend on A");
        assertTrue(nodeC.getDependencies().contains(nodeB), "C should depend on B");

        List<GraphNode> topologicalOrder = graph.getTopologicalOrder();

        assertEquals(3, topologicalOrder.size());

        // A should come before B, B should come before C
        int indexA = -1, indexB = -1, indexC = -1;
        for (int i = 0; i < topologicalOrder.size(); i++) {
            String id = topologicalOrder.get(i).getId();
            if ("A".equals(id)) indexA = i;
            else if ("B".equals(id)) indexB = i;
            else if ("C".equals(id)) indexC = i;
        }

        assertTrue(indexA >= 0 && indexB >= 0 && indexC >= 0, "All components should be found");
        assertTrue(indexA < indexB, "A should come before B");
        assertTrue(indexB < indexC, "B should come before C");
    }
    
    @Test
    void testMigrationOrder() {
        // Create components with different types and priorities
        ComponentModel webXml = new ComponentModel("web", "web.xml", ComponentModel.ComponentType.WEB_XML);
        ComponentModel servlet = new ComponentModel("servlet", "TestServlet", ComponentModel.ComponentType.SERVLET);
        ComponentModel jsp = new ComponentModel("jsp", "test.jsp", ComponentModel.ComponentType.JSP_PAGE);
        
        // JSP depends on servlet, servlet depends on web.xml
        jsp.addDependency("servlet");
        servlet.addDependency("web");
        
        graph.addComponent(webXml);
        graph.addComponent(servlet);
        graph.addComponent(jsp);
        graph.buildDependencies();

        List<GraphNode> migrationOrder = graph.getMigrationOrder();
        
        assertEquals(3, migrationOrder.size());
        
        // web.xml should be first (highest priority and no dependencies)
        assertEquals("web", migrationOrder.get(0).getId());
        assertEquals("servlet", migrationOrder.get(1).getId());
        assertEquals("jsp", migrationOrder.get(2).getId());
    }
    
    @Test
    void testCircularDependencyDetection() {
        ComponentModel compA = new ComponentModel("A", "ComponentA", ComponentModel.ComponentType.JAVA_CLASS);
        ComponentModel compB = new ComponentModel("B", "ComponentB", ComponentModel.ComponentType.JAVA_CLASS);
        ComponentModel compC = new ComponentModel("C", "ComponentC", ComponentModel.ComponentType.JAVA_CLASS);
        
        // Create circular dependency: A -> B -> C -> A
        compA.addDependency("C");
        compB.addDependency("A");
        compC.addDependency("B");
        
        graph.addComponent(compA);
        graph.addComponent(compB);
        graph.addComponent(compC);
        graph.buildDependencies();

        List<List<GraphNode>> cycles = graph.detectCircularDependencies();
        
        assertFalse(cycles.isEmpty());
        assertTrue(cycles.size() >= 1);
    }
    
    @Test
    void testCalculateDepths() {
        // Create a tree structure
        ComponentModel root = new ComponentModel("root", "Root", ComponentModel.ComponentType.WEB_XML);
        ComponentModel level1a = new ComponentModel("l1a", "Level1A", ComponentModel.ComponentType.SERVLET);
        ComponentModel level1b = new ComponentModel("l1b", "Level1B", ComponentModel.ComponentType.FILTER);
        ComponentModel level2 = new ComponentModel("l2", "Level2", ComponentModel.ComponentType.JSP_PAGE);
        
        level1a.addDependency("root");
        level1b.addDependency("root");
        level2.addDependency("l1a");
        
        graph.addComponent(root);
        graph.addComponent(level1a);
        graph.addComponent(level1b);
        graph.addComponent(level2);
        graph.buildDependencies();
        
        graph.calculateDepths();
        
        assertEquals(0, graph.getNode("root").getDepth());
        assertEquals(1, graph.getNode("l1a").getDepth());
        assertEquals(1, graph.getNode("l1b").getDepth());
        assertEquals(2, graph.getNode("l2").getDepth());
        assertEquals(2, graph.getMaxDepth());
    }
    
    @Test
    void testGetRootAndLeafNodes() {
        ComponentModel root1 = new ComponentModel("root1", "Root1", ComponentModel.ComponentType.WEB_XML);
        ComponentModel root2 = new ComponentModel("root2", "Root2", ComponentModel.ComponentType.JAVA_CLASS);
        ComponentModel middle = new ComponentModel("middle", "Middle", ComponentModel.ComponentType.SERVLET);
        ComponentModel leaf1 = new ComponentModel("leaf1", "Leaf1", ComponentModel.ComponentType.JSP_PAGE);
        ComponentModel leaf2 = new ComponentModel("leaf2", "Leaf2", ComponentModel.ComponentType.JSP_PAGE);
        
        middle.addDependency("root1");
        leaf1.addDependency("middle");
        leaf2.addDependency("root2");
        
        graph.addComponent(root1);
        graph.addComponent(root2);
        graph.addComponent(middle);
        graph.addComponent(leaf1);
        graph.addComponent(leaf2);
        graph.buildDependencies();
        
        List<GraphNode> rootNodes = graph.getRootNodes();
        List<GraphNode> leafNodes = graph.getLeafNodes();
        
        assertEquals(2, rootNodes.size());
        assertEquals(2, leafNodes.size());
        
        assertTrue(rootNodes.stream().anyMatch(n -> "root1".equals(n.getId())));
        assertTrue(rootNodes.stream().anyMatch(n -> "root2".equals(n.getId())));
        assertTrue(leafNodes.stream().anyMatch(n -> "leaf1".equals(n.getId())));
        assertTrue(leafNodes.stream().anyMatch(n -> "leaf2".equals(n.getId())));
    }
    
    @Test
    void testGraphStatistics() {
        ComponentModel webXml = new ComponentModel("web", "web.xml", ComponentModel.ComponentType.WEB_XML);
        ComponentModel servlet1 = new ComponentModel("servlet1", "Servlet1", ComponentModel.ComponentType.SERVLET);
        ComponentModel servlet2 = new ComponentModel("servlet2", "Servlet2", ComponentModel.ComponentType.SERVLET);
        ComponentModel jsp = new ComponentModel("jsp", "test.jsp", ComponentModel.ComponentType.JSP_PAGE);
        
        servlet1.addDependency("web");
        servlet2.addDependency("web");
        jsp.addDependency("servlet1");
        
        graph.addComponent(webXml);
        graph.addComponent(servlet1);
        graph.addComponent(servlet2);
        graph.addComponent(jsp);
        graph.buildDependencies();

        DependencyGraph.GraphStatistics stats = graph.getStatistics();
        
        assertEquals(4, stats.totalNodes);
        assertEquals(1, stats.rootNodes);
        assertEquals(2, stats.leafNodes);
        assertEquals(0, stats.circularDependencies);
        
        assertEquals(Integer.valueOf(1), stats.componentTypeCounts.get(ComponentModel.ComponentType.WEB_XML));
        assertEquals(Integer.valueOf(2), stats.componentTypeCounts.get(ComponentModel.ComponentType.SERVLET));
        assertEquals(Integer.valueOf(1), stats.componentTypeCounts.get(ComponentModel.ComponentType.JSP_PAGE));
    }
    
    @Test
    void testEmptyGraph() {
        assertTrue(graph.getAllComponents().isEmpty());
        assertTrue(graph.getAllNodes().isEmpty());
        assertTrue(graph.getRootNodes().isEmpty());
        assertTrue(graph.getLeafNodes().isEmpty());
        assertTrue(graph.getTopologicalOrder().isEmpty());
        assertTrue(graph.getMigrationOrder().isEmpty());
        assertTrue(graph.detectCircularDependencies().isEmpty());
        assertEquals(0, graph.getMaxDepth());
    }
    
    @Test
    void testNodeMigrationPriority() {
        ComponentModel webXml = new ComponentModel("web", "web.xml", ComponentModel.ComponentType.WEB_XML);
        ComponentModel servlet = new ComponentModel("servlet", "TestServlet", ComponentModel.ComponentType.SERVLET);
        ComponentModel jsp = new ComponentModel("jsp", "test.jsp", ComponentModel.ComponentType.JSP_PAGE);
        
        graph.addComponent(webXml);
        graph.addComponent(servlet);
        graph.addComponent(jsp);
        graph.buildDependencies();
        
        GraphNode webXmlNode = graph.getNode("web");
        GraphNode servletNode = graph.getNode("servlet");
        GraphNode jspNode = graph.getNode("jsp");
        
        int webXmlPriority = webXmlNode.calculateMigrationPriority();
        int servletPriority = servletNode.calculateMigrationPriority();
        int jspPriority = jspNode.calculateMigrationPriority();
        
        // web.xml should have highest priority
        assertTrue(webXmlPriority > servletPriority);
        assertTrue(servletPriority > jspPriority);
    }
}
