package com.phodal.legacy.generator;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Test class for TestSuiteGenerator to verify test generation capabilities.
 */
class TestSuiteGeneratorTest {
    
    private TestSuiteGenerator testSuiteGenerator;
    private GenerationConfig config;
    
    @TempDir
    Path tempDir;
    
    @BeforeEach
    void setUp() {
        config = new GenerationConfig();
        config.setBasePackage("com.example.migrated");
        config.setApiPrefix("/api/v1");
        config.setSpringBootVersion("3.2.1");
        config.setJavaVersion("17");
        
        testSuiteGenerator = new TestSuiteGenerator(config);
    }
    
    @Test
    void testGenerateTestSuite() throws IOException {
        // Given
        List<GeneratedClass> entities = createMockEntities();
        List<GeneratedClass> repositories = createMockRepositories();
        List<GeneratedClass> services = createMockServices();
        List<GeneratedClass> controllers = createMockControllers();
        
        // When
        List<GeneratedClass> testClasses = testSuiteGenerator.generateTestSuite(
            entities, repositories, services, controllers, tempDir);
        
        // Then
        assertNotNull(testClasses);
        assertTrue(testClasses.size() > 0);
        
        // Verify test directory structure was created
        assertTrue(Files.exists(tempDir.resolve("src/test/java/com/example/migrated/controller")));
        assertTrue(Files.exists(tempDir.resolve("src/test/java/com/example/migrated/service")));
        assertTrue(Files.exists(tempDir.resolve("src/test/java/com/example/migrated/repository")));
        assertTrue(Files.exists(tempDir.resolve("src/test/java/com/example/migrated/integration")));
        assertTrue(Files.exists(tempDir.resolve("src/test/java/com/example/migrated/testdata")));
        assertTrue(Files.exists(tempDir.resolve("src/test/resources")));
        
        // Verify test configuration was generated
        assertTrue(Files.exists(tempDir.resolve("src/test/resources/application-test.properties")));
        
        // Verify different types of test classes were generated
        long controllerTests = testClasses.stream()
            .filter(tc -> tc.getClassName().endsWith("ControllerTest"))
            .count();
        long serviceTests = testClasses.stream()
            .filter(tc -> tc.getClassName().endsWith("ServiceTest"))
            .count();
        long repositoryTests = testClasses.stream()
            .filter(tc -> tc.getClassName().endsWith("RepositoryTest"))
            .count();
        long integrationTests = testClasses.stream()
            .filter(tc -> tc.getClassName().contains("IntegrationTest"))
            .count();
        long testDataClasses = testClasses.stream()
            .filter(tc -> tc.getType() == GeneratedClass.ClassType.TEST_DATA)
            .count();
        
        assertTrue(controllerTests > 0, "Should generate controller tests");
        assertTrue(serviceTests > 0, "Should generate service tests");
        assertTrue(repositoryTests > 0, "Should generate repository tests");
        assertTrue(integrationTests > 0, "Should generate integration tests");
        assertTrue(testDataClasses > 0, "Should generate test data classes");
    }
    
    @Test
    void testGenerateTestApplicationProperties() throws IOException {
        // Given
        List<GeneratedClass> entities = createMockEntities();
        List<GeneratedClass> repositories = createMockRepositories();
        List<GeneratedClass> services = createMockServices();
        List<GeneratedClass> controllers = createMockControllers();
        
        // When
        testSuiteGenerator.generateTestSuite(entities, repositories, services, controllers, tempDir);
        
        // Then
        Path testPropertiesPath = tempDir.resolve("src/test/resources/application-test.properties");
        assertTrue(Files.exists(testPropertiesPath));
        
        String content = Files.readString(testPropertiesPath);
        assertTrue(content.contains("spring.profiles.active=test"));
        assertTrue(content.contains("spring.datasource.url=jdbc:h2:mem:testdb"));
        assertTrue(content.contains("spring.jpa.hibernate.ddl-auto=create-drop"));
    }
    
    private List<GeneratedClass> createMockEntities() {
        GeneratedClass postEntity = new GeneratedClass();
        postEntity.setClassName("Post");
        postEntity.setPackageName("com.example.migrated.entity");
        postEntity.setType(GeneratedClass.ClassType.ENTITY);
        
        GeneratedClass userEntity = new GeneratedClass();
        userEntity.setClassName("User");
        userEntity.setPackageName("com.example.migrated.entity");
        userEntity.setType(GeneratedClass.ClassType.ENTITY);
        
        return Arrays.asList(postEntity, userEntity);
    }
    
    private List<GeneratedClass> createMockRepositories() {
        GeneratedClass postRepository = new GeneratedClass();
        postRepository.setClassName("PostRepository");
        postRepository.setPackageName("com.example.migrated.repository");
        postRepository.setType(GeneratedClass.ClassType.REPOSITORY);
        
        GeneratedClass userRepository = new GeneratedClass();
        userRepository.setClassName("UserRepository");
        userRepository.setPackageName("com.example.migrated.repository");
        userRepository.setType(GeneratedClass.ClassType.REPOSITORY);
        
        return Arrays.asList(postRepository, userRepository);
    }
    
    private List<GeneratedClass> createMockServices() {
        GeneratedClass postService = new GeneratedClass();
        postService.setClassName("PostService");
        postService.setPackageName("com.example.migrated.service");
        postService.setType(GeneratedClass.ClassType.SERVICE);
        
        GeneratedClass userService = new GeneratedClass();
        userService.setClassName("UserService");
        userService.setPackageName("com.example.migrated.service");
        userService.setType(GeneratedClass.ClassType.SERVICE);
        
        return Arrays.asList(postService, userService);
    }
    
    private List<GeneratedClass> createMockControllers() {
        GeneratedClass postController = new GeneratedClass();
        postController.setClassName("PostController");
        postController.setPackageName("com.example.migrated.controller");
        postController.setType(GeneratedClass.ClassType.CONTROLLER);
        
        GeneratedClass userController = new GeneratedClass();
        userController.setClassName("UserController");
        userController.setPackageName("com.example.migrated.controller");
        userController.setType(GeneratedClass.ClassType.CONTROLLER);
        
        return Arrays.asList(postController, userController);
    }
}
