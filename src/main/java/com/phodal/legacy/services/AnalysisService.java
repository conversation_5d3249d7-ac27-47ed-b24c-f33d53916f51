package com.phodal.legacy.services;

import com.phodal.legacy.mapper.JspBytecodeMapper;
import com.phodal.legacy.model.ComponentModel;
import com.phodal.legacy.model.DependencyGraph;
import com.phodal.legacy.model.GraphNode;
import com.phodal.legacy.model.MappingRegistry;
import com.phodal.legacy.parser.*;
import com.phodal.legacy.utils.FileUtils;
import com.phodal.legacy.utils.LoggingUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.nio.file.Path;
import java.util.*;

/**
 * Service that coordinates all analysis components to analyze legacy JSP projects.
 * Integrates JSP, Java, bytecode, and web.xml analyzers to build a comprehensive project model.
 */
public class AnalysisService {
    
    private static final Logger logger = LoggerFactory.getLogger(AnalysisService.class);
    
    private final JspAnalyzer jspAnalyzer;
    private final JavaCodeAnalyzer javaAnalyzer;
    private final BytecodeAnalyzer bytecodeAnalyzer;
    private final WebXmlParser webXmlParser;
    private final JspBytecodeMapper jspBytecodeMapper;

    public AnalysisService() {
        this.jspAnalyzer = new JspAnalyzer();
        this.javaAnalyzer = new JavaCodeAnalyzer();
        this.bytecodeAnalyzer = new BytecodeAnalyzer();
        this.webXmlParser = new WebXmlParser();
        this.jspBytecodeMapper = new JspBytecodeMapper();
    }
    
    /**
     * Perform comprehensive analysis of a legacy JSP project
     */
    public AnalysisResult analyzeProject(Path projectRoot, AnalysisOptions options) throws IOException {
        LoggingUtils.logOperationStart("analyzeProject", "AnalysisService");
        
        if (!projectRoot.toFile().exists() || !projectRoot.toFile().isDirectory()) {
            throw new IllegalArgumentException("Project root does not exist or is not a directory: " + projectRoot);
        }
        
        logger.info("Starting comprehensive analysis of project: {}", projectRoot.toAbsolutePath());
        
        AnalysisResult result = new AnalysisResult();
        result.setProjectRoot(projectRoot);
        result.setAnalysisOptions(options);
        result.setStartTime(System.currentTimeMillis());

        // Initialize empty lists
        result.setJspComponents(new ArrayList<>());
        result.setJavaComponents(new ArrayList<>());
        result.setBytecodeComponents(new ArrayList<>());
        result.setWebXmlComponents(new ArrayList<>());
        
        List<ComponentModel> allComponents = new ArrayList<>();
        
        try {
            // Analyze JSP files
            if (options.isIncludeJsp()) {
                logger.info("Analyzing JSP files...");
                List<ComponentModel> jspComponents = jspAnalyzer.analyzeJspFiles(projectRoot);
                allComponents.addAll(jspComponents);
                result.getJspComponents().addAll(jspComponents);
                logger.info("Found {} JSP components", jspComponents.size());
            }
            
            // Analyze Java source files
            if (options.isIncludeJava()) {
                logger.info("Analyzing Java source files...");
                List<ComponentModel> javaComponents = javaAnalyzer.analyzeJavaFiles(projectRoot);
                allComponents.addAll(javaComponents);
                result.getJavaComponents().addAll(javaComponents);
                logger.info("Found {} Java components", javaComponents.size());
            }

            // Analyze bytecode files
            if (options.isIncludeBytecode()) {
                logger.info("Analyzing bytecode files...");
                List<ComponentModel> bytecodeComponents = bytecodeAnalyzer.analyzeBytecode(projectRoot);
                allComponents.addAll(bytecodeComponents);
                result.getBytecodeComponents().addAll(bytecodeComponents);
                logger.info("Found {} bytecode components", bytecodeComponents.size());
            }

            // Analyze web.xml files
            if (options.isIncludeWebXml()) {
                logger.info("Analyzing web.xml files...");
                List<ComponentModel> webXmlComponents = webXmlParser.analyzeWebXmlFiles(projectRoot);
                allComponents.addAll(webXmlComponents);
                result.getWebXmlComponents().addAll(webXmlComponents);
                logger.info("Found {} web.xml components", webXmlComponents.size());
            }
            
            // Build JSP to bytecode mappings
            logger.info("Building JSP to bytecode mappings...");
            MappingRegistry mappingRegistry = buildJspBytecodeMappings(result);
            result.setMappingRegistry(mappingRegistry);

            // Build dependency graph
            logger.info("Building dependency graph...");
            DependencyGraph dependencyGraph = buildDependencyGraph(allComponents);
            result.setDependencyGraph(dependencyGraph);

            // Generate analysis summary
            AnalysisSummary summary = generateAnalysisSummary(result);
            result.setSummary(summary);
            
            result.setEndTime(System.currentTimeMillis());
            result.setSuccess(true);
            
            LoggingUtils.logOperationComplete("analyzeProject", "AnalysisService");
            logger.info("Analysis completed successfully in {} ms", result.getDuration());
            
            return result;
            
        } catch (Exception e) {
            result.setEndTime(System.currentTimeMillis());
            result.setSuccess(false);
            result.setErrorMessage(e.getMessage());
            
            LoggingUtils.logOperationError("analyzeProject", "AnalysisService", e);
            throw e;
        }
    }
    
    /**
     * Build JSP to bytecode mappings
     */
    private MappingRegistry buildJspBytecodeMappings(AnalysisResult result) {
        List<ComponentModel> jspComponents = result.getJspComponents();
        List<ComponentModel> bytecodeComponents = result.getBytecodeComponents();

        if (jspComponents.isEmpty() || bytecodeComponents.isEmpty()) {
            logger.info("Skipping mapping creation - no JSP or bytecode components found");
            return new MappingRegistry();
        }

        return jspBytecodeMapper.createMappings(jspComponents, bytecodeComponents);
    }

    /**
     * Build dependency graph from all components
     */
    private DependencyGraph buildDependencyGraph(List<ComponentModel> components) {
        DependencyGraph graph = new DependencyGraph();

        // Add all components to the graph
        graph.addComponents(components);

        // Build dependency relationships
        graph.buildDependencies();

        return graph;
    }
    
    /**
     * Generate analysis summary
     */
    private AnalysisSummary generateAnalysisSummary(AnalysisResult result) {
        AnalysisSummary summary = new AnalysisSummary();
        
        // Count components by type
        Map<ComponentModel.ComponentType, Integer> componentCounts = new HashMap<>();
        for (ComponentModel component : getAllComponents(result)) {
            componentCounts.merge(component.getType(), 1, Integer::sum);
        }
        summary.setComponentCounts(componentCounts);
        
        // Get dependency graph statistics
        if (result.getDependencyGraph() != null) {
            DependencyGraph.GraphStatistics graphStats = result.getDependencyGraph().getStatistics();
            summary.setGraphStatistics(graphStats);
            
            // Calculate migration complexity
            summary.setMigrationComplexity(calculateMigrationComplexity(result.getDependencyGraph()));
            
            // Get migration order
            List<GraphNode> migrationOrder = result.getDependencyGraph().getMigrationOrder();
            summary.setMigrationOrder(migrationOrder);
        }
        
        // Add mapping statistics
        if (result.getMappingRegistry() != null) {
            MappingRegistry.MappingStatistics mappingStats = result.getMappingRegistry().getStatistics();
            summary.setMappingStatistics(mappingStats);
        }

        // Calculate project size
        try {
            long projectSize = FileUtils.calculateDirectorySize(result.getProjectRoot());
            summary.setProjectSize(projectSize);
        } catch (IOException e) {
            logger.warn("Failed to calculate project size: {}", e.getMessage());
        }

        return summary;
    }
    
    /**
     * Calculate migration complexity score
     */
    private int calculateMigrationComplexity(DependencyGraph graph) {
        int complexity = 0;
        
        for (GraphNode node : graph.getAllNodes()) {
            ComponentModel component = node.getComponent();
            
            // Base complexity by component type
            switch (component.getType()) {
                case WEB_XML:
                    complexity += 50;
                    break;
                case SERVLET:
                    complexity += 40;
                    break;
                case FILTER:
                    complexity += 35;
                    break;
                case LISTENER:
                    complexity += 30;
                    break;
                case JSP_PAGE:
                    complexity += 25;
                    break;
                case JAVA_CLASS:
                    complexity += 20;
                    break;
                default:
                    complexity += 10;
            }
            
            // Add complexity based on dependencies
            complexity += node.getDependencies().size() * 5;
            
            // Add complexity based on component-specific complexity
            Object componentComplexity = component.getProperty("complexity");
            if (componentComplexity instanceof Integer) {
                complexity += (Integer) componentComplexity;
            }
        }
        
        // Add complexity for circular dependencies
        List<List<GraphNode>> cycles = graph.detectCircularDependencies();
        complexity += cycles.size() * 100;
        
        return complexity;
    }
    
    /**
     * Get all components from analysis result
     */
    private List<ComponentModel> getAllComponents(AnalysisResult result) {
        List<ComponentModel> allComponents = new ArrayList<>();
        
        if (result.getJspComponents() != null) {
            allComponents.addAll(result.getJspComponents());
        }
        if (result.getJavaComponents() != null) {
            allComponents.addAll(result.getJavaComponents());
        }
        if (result.getBytecodeComponents() != null) {
            allComponents.addAll(result.getBytecodeComponents());
        }
        if (result.getWebXmlComponents() != null) {
            allComponents.addAll(result.getWebXmlComponents());
        }
        
        return allComponents;
    }
    
    /**
     * Analysis options configuration
     */
    public static class AnalysisOptions {
        private boolean includeJsp = true;
        private boolean includeJava = true;
        private boolean includeBytecode = false;
        private boolean includeWebXml = true;
        private boolean generateReport = true;
        private String reportFormat = "detailed"; // detailed, summary, json
        
        // Getters and setters
        public boolean isIncludeJsp() { return includeJsp; }
        public void setIncludeJsp(boolean includeJsp) { this.includeJsp = includeJsp; }
        
        public boolean isIncludeJava() { return includeJava; }
        public void setIncludeJava(boolean includeJava) { this.includeJava = includeJava; }
        
        public boolean isIncludeBytecode() { return includeBytecode; }
        public void setIncludeBytecode(boolean includeBytecode) { this.includeBytecode = includeBytecode; }
        
        public boolean isIncludeWebXml() { return includeWebXml; }
        public void setIncludeWebXml(boolean includeWebXml) { this.includeWebXml = includeWebXml; }
        
        public boolean isGenerateReport() { return generateReport; }
        public void setGenerateReport(boolean generateReport) { this.generateReport = generateReport; }
        
        public String getReportFormat() { return reportFormat; }
        public void setReportFormat(String reportFormat) { this.reportFormat = reportFormat; }
    }
    
    /**
     * Analysis result container
     */
    public static class AnalysisResult {
        private Path projectRoot;
        private AnalysisOptions analysisOptions;
        private long startTime;
        private long endTime;
        private boolean success;
        private String errorMessage;
        
        private List<ComponentModel> jspComponents;
        private List<ComponentModel> javaComponents;
        private List<ComponentModel> bytecodeComponents;
        private List<ComponentModel> webXmlComponents;

        private DependencyGraph dependencyGraph;
        private MappingRegistry mappingRegistry;
        private AnalysisSummary summary;
        
        // Getters and setters
        public Path getProjectRoot() { return projectRoot; }
        public void setProjectRoot(Path projectRoot) { this.projectRoot = projectRoot; }
        
        public AnalysisOptions getAnalysisOptions() { return analysisOptions; }
        public void setAnalysisOptions(AnalysisOptions analysisOptions) { this.analysisOptions = analysisOptions; }
        
        public long getStartTime() { return startTime; }
        public void setStartTime(long startTime) { this.startTime = startTime; }
        
        public long getEndTime() { return endTime; }
        public void setEndTime(long endTime) { this.endTime = endTime; }
        
        public long getDuration() { return endTime - startTime; }
        
        public boolean isSuccess() { return success; }
        public void setSuccess(boolean success) { this.success = success; }
        
        public String getErrorMessage() { return errorMessage; }
        public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }
        
        public List<ComponentModel> getJspComponents() { return jspComponents; }
        public void setJspComponents(List<ComponentModel> jspComponents) { this.jspComponents = jspComponents; }
        
        public List<ComponentModel> getJavaComponents() { return javaComponents; }
        public void setJavaComponents(List<ComponentModel> javaComponents) { this.javaComponents = javaComponents; }
        
        public List<ComponentModel> getBytecodeComponents() { return bytecodeComponents; }
        public void setBytecodeComponents(List<ComponentModel> bytecodeComponents) { this.bytecodeComponents = bytecodeComponents; }
        
        public List<ComponentModel> getWebXmlComponents() { return webXmlComponents; }
        public void setWebXmlComponents(List<ComponentModel> webXmlComponents) { this.webXmlComponents = webXmlComponents; }
        
        public DependencyGraph getDependencyGraph() { return dependencyGraph; }
        public void setDependencyGraph(DependencyGraph dependencyGraph) { this.dependencyGraph = dependencyGraph; }

        public MappingRegistry getMappingRegistry() { return mappingRegistry; }
        public void setMappingRegistry(MappingRegistry mappingRegistry) { this.mappingRegistry = mappingRegistry; }

        public AnalysisSummary getSummary() { return summary; }
        public void setSummary(AnalysisSummary summary) { this.summary = summary; }
    }
    
    /**
     * Analysis summary container
     */
    public static class AnalysisSummary {
        private Map<ComponentModel.ComponentType, Integer> componentCounts;
        private DependencyGraph.GraphStatistics graphStatistics;
        private MappingRegistry.MappingStatistics mappingStatistics;
        private int migrationComplexity;
        private List<GraphNode> migrationOrder;
        private long projectSize;
        
        // Getters and setters
        public Map<ComponentModel.ComponentType, Integer> getComponentCounts() { return componentCounts; }
        public void setComponentCounts(Map<ComponentModel.ComponentType, Integer> componentCounts) { this.componentCounts = componentCounts; }
        
        public DependencyGraph.GraphStatistics getGraphStatistics() { return graphStatistics; }
        public void setGraphStatistics(DependencyGraph.GraphStatistics graphStatistics) { this.graphStatistics = graphStatistics; }

        public MappingRegistry.MappingStatistics getMappingStatistics() { return mappingStatistics; }
        public void setMappingStatistics(MappingRegistry.MappingStatistics mappingStatistics) { this.mappingStatistics = mappingStatistics; }

        public int getMigrationComplexity() { return migrationComplexity; }
        public void setMigrationComplexity(int migrationComplexity) { this.migrationComplexity = migrationComplexity; }
        
        public List<GraphNode> getMigrationOrder() { return migrationOrder; }
        public void setMigrationOrder(List<GraphNode> migrationOrder) { this.migrationOrder = migrationOrder; }
        
        public long getProjectSize() { return projectSize; }
        public void setProjectSize(long projectSize) { this.projectSize = projectSize; }
    }
}
