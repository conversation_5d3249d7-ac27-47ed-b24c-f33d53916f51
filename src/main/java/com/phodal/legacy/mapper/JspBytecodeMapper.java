package com.phodal.legacy.mapper;

import com.phodal.legacy.model.ComponentModel;
import com.phodal.legacy.model.JspBytecodeMapping;
import com.phodal.legacy.model.MappingRegistry;
import com.phodal.legacy.utils.LoggingUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Maps JSP files to their compiled bytecode counterparts.
 * This class analyzes JSP components and bytecode components to establish
 * relationships between source JSP files and their compiled servlet classes.
 */
public class JspBytecodeMapper {
    
    private static final Logger logger = LoggerFactory.getLogger(JspBytecodeMapper.class);
    
    // Patterns for JSP to servlet class name conversion
    private static final Pattern JSP_NAME_PATTERN = Pattern.compile("([^/\\\\]+)\\.jsp$", Pattern.CASE_INSENSITIVE);
    private static final Pattern JSPX_NAME_PATTERN = Pattern.compile("([^/\\\\]+)\\.jspx$", Pattern.CASE_INSENSITIVE);
    
    // Common JSP servlet class naming conventions
    private static final String[] SERVLET_NAME_PATTERNS = {
        "%s_jsp",           // Tomcat style: index_jsp
        "%sJSP",            // WebLogic style: indexJSP  
        "%s$jsp",           // Some containers: index$jsp
        "jsp.%s_jsp",       // Package style: jsp.index_jsp
        "_jsp._%s_jsp"      // Jasper style: _jsp._index_jsp
    };
    
    private final MappingRegistry mappingRegistry;
    
    public JspBytecodeMapper() {
        this.mappingRegistry = new MappingRegistry();
    }
    
    public JspBytecodeMapper(MappingRegistry mappingRegistry) {
        this.mappingRegistry = mappingRegistry;
    }
    
    /**
     * Create mappings between JSP components and bytecode components
     */
    public MappingRegistry createMappings(List<ComponentModel> jspComponents, 
                                        List<ComponentModel> bytecodeComponents) {
        LoggingUtils.logOperationStart("createMappings", "JspBytecodeMapper");
        
        logger.info("Creating mappings for {} JSP components and {} bytecode components", 
                   jspComponents.size(), bytecodeComponents.size());
        
        // Build lookup maps for efficient searching
        Map<String, ComponentModel> bytecodeClassMap = buildBytecodeClassMap(bytecodeComponents);
        
        int mappedCount = 0;
        for (ComponentModel jspComponent : jspComponents) {
            if (jspComponent.getType() == ComponentModel.ComponentType.JSP_PAGE) {
                try {
                    JspBytecodeMapping mapping = createMapping(jspComponent, bytecodeClassMap);
                    if (mapping != null) {
                        mappingRegistry.registerMapping(mapping);
                        mappedCount++;
                        logger.debug("Created mapping for JSP: {} -> {}", 
                                   jspComponent.getName(), mapping.getCompiledServletClass());
                    }
                } catch (Exception e) {
                    logger.warn("Failed to create mapping for JSP: {} - {}", 
                              jspComponent.getName(), e.getMessage());
                }
            }
        }
        
        logger.info("Successfully created {} mappings out of {} JSP components", 
                   mappedCount, jspComponents.size());
        LoggingUtils.logOperationComplete("createMappings", "JspBytecodeMapper");
        
        return mappingRegistry;
    }
    
    /**
     * Create a single mapping between JSP component and its bytecode counterpart
     */
    private JspBytecodeMapping createMapping(ComponentModel jspComponent, 
                                           Map<String, ComponentModel> bytecodeClassMap) {
        
        String jspPath = jspComponent.getSourcePath();
        String jspName = extractJspName(jspComponent.getName());
        
        if (jspName == null) {
            logger.warn("Could not extract JSP name from: {}", jspComponent.getName());
            return null;
        }
        
        // Try to find the corresponding servlet class
        ComponentModel servletComponent = findServletComponent(jspName, bytecodeClassMap);
        if (servletComponent == null) {
            logger.debug("No servlet class found for JSP: {}", jspName);
            return null;
        }
        
        // Create the mapping
        JspBytecodeMapping mapping = new JspBytecodeMapping(jspComponent.getId(), jspPath);
        mapping.setCompiledServletClass(getClassName(servletComponent));
        
        // Map class names
        mapClassNames(jspComponent, servletComponent, mapping);
        
        // Map methods
        mapMethods(jspComponent, servletComponent, mapping);
        
        // Map dependencies
        mapDependencies(jspComponent, servletComponent, mapping);
        
        // Map tag libraries
        mapTagLibraries(jspComponent, mapping);
        
        // Add metadata
        addMappingMetadata(jspComponent, servletComponent, mapping);
        
        return mapping;
    }
    
    /**
     * Build a map of bytecode components by class name for efficient lookup
     */
    private Map<String, ComponentModel> buildBytecodeClassMap(List<ComponentModel> bytecodeComponents) {
        Map<String, ComponentModel> classMap = new HashMap<>();
        
        for (ComponentModel component : bytecodeComponents) {
            if (component.getType() == ComponentModel.ComponentType.JAVA_CLASS ||
                component.getType() == ComponentModel.ComponentType.SERVLET) {
                
                String className = getClassName(component);
                if (className != null) {
                    classMap.put(className.toLowerCase(), component);
                    
                    // Also add simple class name for lookup
                    String simpleClassName = getSimpleClassName(className);
                    if (simpleClassName != null) {
                        classMap.put(simpleClassName.toLowerCase(), component);
                    }
                }
            }
        }
        
        return classMap;
    }
    
    /**
     * Find the servlet component corresponding to a JSP
     */
    private ComponentModel findServletComponent(String jspName, Map<String, ComponentModel> bytecodeClassMap) {
        String baseJspName = jspName.toLowerCase();
        
        // Try different servlet naming patterns
        for (String pattern : SERVLET_NAME_PATTERNS) {
            String servletName = String.format(pattern, baseJspName).toLowerCase();
            ComponentModel component = bytecodeClassMap.get(servletName);
            if (component != null) {
                return component;
            }
        }
        
        // Try direct name match
        ComponentModel component = bytecodeClassMap.get(baseJspName);
        if (component != null) {
            return component;
        }
        
        // Try fuzzy matching
        return findServletComponentFuzzy(baseJspName, bytecodeClassMap);
    }
    
    /**
     * Fuzzy matching for servlet components
     */
    private ComponentModel findServletComponentFuzzy(String jspName, Map<String, ComponentModel> bytecodeClassMap) {
        for (Map.Entry<String, ComponentModel> entry : bytecodeClassMap.entrySet()) {
            String className = entry.getKey();
            if (className.contains(jspName) || jspName.contains(className)) {
                // Additional validation to ensure it's likely a servlet
                ComponentModel component = entry.getValue();
                if (isLikelyServletClass(component)) {
                    return component;
                }
            }
        }
        return null;
    }
    
    /**
     * Check if a component is likely a servlet class
     */
    private boolean isLikelyServletClass(ComponentModel component) {
        // Check if it has servlet-related dependencies or annotations
        Set<String> dependencies = component.getDependencies();
        
        return dependencies.stream().anyMatch(dep -> 
            dep.contains("javax.servlet") || 
            dep.contains("jakarta.servlet") ||
            dep.contains("HttpServlet") ||
            dep.contains("GenericServlet")
        );
    }
    
    /**
     * Extract JSP name from file name
     */
    private String extractJspName(String fileName) {
        Matcher matcher = JSP_NAME_PATTERN.matcher(fileName);
        if (matcher.find()) {
            return matcher.group(1);
        }
        
        matcher = JSPX_NAME_PATTERN.matcher(fileName);
        if (matcher.find()) {
            return matcher.group(1);
        }
        
        return null;
    }
    
    /**
     * Get class name from component
     */
    private String getClassName(ComponentModel component) {
        Object className = component.getProperty("className");
        if (className != null) {
            return className.toString();
        }
        return component.getName();
    }
    
    /**
     * Get simple class name (without package)
     */
    private String getSimpleClassName(String fullClassName) {
        if (fullClassName == null) return null;
        int lastDot = fullClassName.lastIndexOf('.');
        return lastDot >= 0 ? fullClassName.substring(lastDot + 1) : fullClassName;
    }
    
    /**
     * Map class names between JSP and bytecode
     */
    private void mapClassNames(ComponentModel jspComponent, ComponentModel servletComponent, 
                              JspBytecodeMapping mapping) {
        String jspClassName = jspComponent.getName();
        String servletClassName = getClassName(servletComponent);
        
        if (jspClassName != null && servletClassName != null) {
            mapping.addClassMapping(jspClassName, servletClassName);
        }
    }
    
    /**
     * Map methods between JSP and bytecode
     */
    private void mapMethods(ComponentModel jspComponent, ComponentModel servletComponent, 
                           JspBytecodeMapping mapping) {
        // Extract method information from bytecode component
        Object methods = servletComponent.getProperty("methods");
        if (methods instanceof List) {
            @SuppressWarnings("unchecked")
            List<String> methodList = (List<String>) methods;
            
            // Map common servlet methods
            for (String method : methodList) {
                if (method.contains("_jspService") || method.contains("doGet") || method.contains("doPost")) {
                    mapping.addMethodMapping("jspService", method, 0, "servlet_method");
                }
            }
        }
    }
    
    /**
     * Map dependencies between JSP and bytecode
     */
    private void mapDependencies(ComponentModel jspComponent, ComponentModel servletComponent, 
                                JspBytecodeMapping mapping) {
        Set<String> jspDependencies = jspComponent.getDependencies();
        Set<String> servletDependencies = servletComponent.getDependencies();
        
        // Map common dependencies
        for (String jspDep : jspDependencies) {
            for (String servletDep : servletDependencies) {
                if (isRelatedDependency(jspDep, servletDep)) {
                    mapping.addDependencyMapping(jspDep, servletDep);
                }
            }
        }
    }
    
    /**
     * Check if two dependencies are related
     */
    private boolean isRelatedDependency(String jspDep, String servletDep) {
        if (jspDep.equals(servletDep)) {
            return true;
        }
        
        // Check if they refer to the same class with different formats
        String jspSimple = getSimpleClassName(jspDep);
        String servletSimple = getSimpleClassName(servletDep);
        
        return jspSimple != null && jspSimple.equals(servletSimple);
    }
    
    /**
     * Map tag libraries
     */
    private void mapTagLibraries(ComponentModel jspComponent, JspBytecodeMapping mapping) {
        Object tagLibs = jspComponent.getProperty("tagLibraries");
        if (tagLibs instanceof List) {
            @SuppressWarnings("unchecked")
            List<String> tagLibList = (List<String>) tagLibs;
            
            for (String tagLib : tagLibList) {
                mapping.addTagLibraryMapping(tagLib);
            }
        }
    }
    
    /**
     * Add mapping metadata
     */
    private void addMappingMetadata(ComponentModel jspComponent, ComponentModel servletComponent, 
                                   JspBytecodeMapping mapping) {
        mapping.addMetadata("jspComplexity", jspComponent.getProperty("complexity"));
        mapping.addMetadata("servletComplexity", servletComponent.getProperty("complexity"));
        mapping.addMetadata("jspSize", jspComponent.getProperty("size"));
        mapping.addMetadata("mappingConfidence", calculateMappingConfidence(jspComponent, servletComponent));
    }
    
    /**
     * Calculate confidence score for the mapping
     */
    private double calculateMappingConfidence(ComponentModel jspComponent, ComponentModel servletComponent) {
        double confidence = 0.5; // Base confidence
        
        // Increase confidence based on naming similarity
        String jspName = extractJspName(jspComponent.getName());
        String servletName = getSimpleClassName(getClassName(servletComponent));
        
        if (jspName != null && servletName != null) {
            if (servletName.toLowerCase().contains(jspName.toLowerCase())) {
                confidence += 0.3;
            }
        }
        
        // Increase confidence based on dependency overlap
        Set<String> jspDeps = jspComponent.getDependencies();
        Set<String> servletDeps = servletComponent.getDependencies();
        
        long commonDeps = jspDeps.stream()
                                .filter(servletDeps::contains)
                                .count();
        
        if (!jspDeps.isEmpty()) {
            confidence += (double) commonDeps / jspDeps.size() * 0.2;
        }
        
        return Math.min(confidence, 1.0);
    }
    
    /**
     * Get the mapping registry
     */
    public MappingRegistry getMappingRegistry() {
        return mappingRegistry;
    }
}
