package com.phodal.legacy.parser;

import com.phodal.legacy.model.ComponentModel;
import com.phodal.legacy.utils.FileUtils;
import com.phodal.legacy.utils.LoggingUtils;
import org.dom4j.Document;
import org.dom4j.DocumentException;
import org.dom4j.Element;
import org.dom4j.io.SAXReader;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.*;
import java.util.stream.Stream;

/**
 * Parser for web.xml files to extract servlet mappings, filters, listeners, and configuration.
 * Analyzes web application deployment descriptors for Spring Boot migration.
 */
public class WebXmlParser {
    
    private static final Logger logger = LoggerFactory.getLogger(WebXmlParser.class);
    
    /**
     * Find and analyze web.xml files in the project
     */
    public List<ComponentModel> analyzeWebXmlFiles(Path projectRoot) throws IOException {
        LoggingUtils.logOperationStart("analyzeWebXmlFiles", "WebXmlParser");
        
        List<ComponentModel> webXmlComponents = new ArrayList<>();
        
        try (Stream<Path> paths = Files.walk(projectRoot)) {
            List<Path> webXmlFiles = paths
                .filter(Files::isRegularFile)
                .filter(path -> isWebXmlFile(path))
                .toList();
            
            logger.info("Found {} web.xml files to analyze", webXmlFiles.size());
            
            for (int i = 0; i < webXmlFiles.size(); i++) {
                Path webXmlFile = webXmlFiles.get(i);
                LoggingUtils.logProgress(i + 1, webXmlFiles.size(), 
                    "Analyzing web.xml: " + webXmlFile.getFileName());
                
                try {
                    ComponentModel component = analyzeWebXmlFile(webXmlFile, projectRoot);
                    if (component != null) {
                        webXmlComponents.add(component);
                    }
                } catch (Exception e) {
                    logger.warn("Failed to analyze web.xml file: {} - {}", webXmlFile, e.getMessage());
                }
            }
            
            LoggingUtils.logOperationComplete("analyzeWebXmlFiles", "WebXmlParser");
            return webXmlComponents;
            
        } catch (IOException e) {
            LoggingUtils.logOperationError("analyzeWebXmlFiles", "WebXmlParser", e);
            throw e;
        }
    }
    
    /**
     * Analyze a single web.xml file
     */
    public ComponentModel analyzeWebXmlFile(Path webXmlFile, Path projectRoot) throws IOException, DocumentException {
        LoggingUtils.logFileProcessing(webXmlFile.toString(), "Analyzing web.xml");
        
        String relativePath = FileUtils.getRelativePath(projectRoot, webXmlFile);
        
        ComponentModel component = new ComponentModel(
            relativePath,
            webXmlFile.getFileName().toString(),
            ComponentModel.ComponentType.WEB_XML
        );
        component.setSourcePath(webXmlFile.toString());

        // Parse XML document
        SAXReader reader = new SAXReader();
        Document document = reader.read(webXmlFile.toFile());
        Element root = document.getRootElement();

        // Extract web.xml metadata
        WebXmlMetadata metadata = extractWebXmlMetadata(root);
        component.getProperties().putAll(metadata.toMap());

        // Extract dependencies (servlet classes, filter classes, etc.)
        Set<String> dependencies = extractDependencies(root);
        component.getDependencies().addAll(dependencies);
        
        LoggingUtils.logFileProcessed(webXmlFile.toString(), "Analyzed web.xml");
        return component;
    }
    
    /**
     * Check if a file is a web.xml file
     */
    private boolean isWebXmlFile(Path path) {
        String fileName = path.getFileName().toString().toLowerCase();
        return fileName.equals("web.xml");
    }
    
    /**
     * Extract web.xml metadata
     */
    private WebXmlMetadata extractWebXmlMetadata(Element root) {
        WebXmlMetadata metadata = new WebXmlMetadata();
        
        // Extract basic information
        metadata.setVersion(root.attributeValue("version"));
        metadata.setDisplayName(getElementText(root, "display-name"));
        metadata.setDescription(getElementText(root, "description"));
        
        // Extract servlets
        List<Element> servlets = root.elements("servlet");
        for (Element servlet : servlets) {
            ServletInfo servletInfo = new ServletInfo();
            servletInfo.setName(getElementText(servlet, "servlet-name"));
            servletInfo.setClassName(getElementText(servlet, "servlet-class"));
            servletInfo.setJspFile(getElementText(servlet, "jsp-file"));
            
            // Extract init parameters
            List<Element> initParams = servlet.elements("init-param");
            for (Element initParam : initParams) {
                String paramName = getElementText(initParam, "param-name");
                String paramValue = getElementText(initParam, "param-value");
                servletInfo.addInitParam(paramName, paramValue);
            }
            
            metadata.addServlet(servletInfo);
        }
        
        // Extract servlet mappings
        List<Element> servletMappings = root.elements("servlet-mapping");
        for (Element mapping : servletMappings) {
            String servletName = getElementText(mapping, "servlet-name");
            String urlPattern = getElementText(mapping, "url-pattern");
            metadata.addServletMapping(servletName, urlPattern);
        }
        
        // Extract filters
        List<Element> filters = root.elements("filter");
        for (Element filter : filters) {
            FilterInfo filterInfo = new FilterInfo();
            filterInfo.setName(getElementText(filter, "filter-name"));
            filterInfo.setClassName(getElementText(filter, "filter-class"));
            
            // Extract init parameters
            List<Element> initParams = filter.elements("init-param");
            for (Element initParam : initParams) {
                String paramName = getElementText(initParam, "param-name");
                String paramValue = getElementText(initParam, "param-value");
                filterInfo.addInitParam(paramName, paramValue);
            }
            
            metadata.addFilter(filterInfo);
        }
        
        // Extract filter mappings
        List<Element> filterMappings = root.elements("filter-mapping");
        for (Element mapping : filterMappings) {
            String filterName = getElementText(mapping, "filter-name");
            String urlPattern = getElementText(mapping, "url-pattern");
            String servletName = getElementText(mapping, "servlet-name");
            metadata.addFilterMapping(filterName, urlPattern, servletName);
        }
        
        // Extract listeners
        List<Element> listeners = root.elements("listener");
        for (Element listener : listeners) {
            String listenerClass = getElementText(listener, "listener-class");
            metadata.addListener(listenerClass);
        }
        
        // Extract context parameters
        List<Element> contextParams = root.elements("context-param");
        for (Element contextParam : contextParams) {
            String paramName = getElementText(contextParam, "param-name");
            String paramValue = getElementText(contextParam, "param-value");
            metadata.addContextParam(paramName, paramValue);
        }
        
        // Extract welcome files
        Element welcomeFileList = root.element("welcome-file-list");
        if (welcomeFileList != null) {
            List<Element> welcomeFiles = welcomeFileList.elements("welcome-file");
            for (Element welcomeFile : welcomeFiles) {
                metadata.addWelcomeFile(welcomeFile.getTextTrim());
            }
        }
        
        // Extract error pages
        List<Element> errorPages = root.elements("error-page");
        for (Element errorPage : errorPages) {
            String errorCode = getElementText(errorPage, "error-code");
            String exceptionType = getElementText(errorPage, "exception-type");
            String location = getElementText(errorPage, "location");
            metadata.addErrorPage(errorCode, exceptionType, location);
        }
        
        return metadata;
    }
    
    /**
     * Extract dependencies from web.xml
     */
    private Set<String> extractDependencies(Element root) {
        Set<String> dependencies = new HashSet<>();
        
        // Extract servlet classes
        List<Element> servlets = root.elements("servlet");
        for (Element servlet : servlets) {
            String servletClass = getElementText(servlet, "servlet-class");
            if (servletClass != null && !servletClass.trim().isEmpty()) {
                dependencies.add(servletClass);
            }
        }
        
        // Extract filter classes
        List<Element> filters = root.elements("filter");
        for (Element filter : filters) {
            String filterClass = getElementText(filter, "filter-class");
            if (filterClass != null && !filterClass.trim().isEmpty()) {
                dependencies.add(filterClass);
            }
        }
        
        // Extract listener classes
        List<Element> listeners = root.elements("listener");
        for (Element listener : listeners) {
            String listenerClass = getElementText(listener, "listener-class");
            if (listenerClass != null && !listenerClass.trim().isEmpty()) {
                dependencies.add(listenerClass);
            }
        }
        
        return dependencies;
    }
    
    /**
     * Helper method to get element text safely
     */
    private String getElementText(Element parent, String elementName) {
        Element element = parent.element(elementName);
        return element != null ? element.getTextTrim() : null;
    }
    
    /**
     * Servlet information container
     */
    public static class ServletInfo {
        private String name;
        private String className;
        private String jspFile;
        private Map<String, String> initParams = new HashMap<>();
        
        // Getters and setters
        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
        
        public String getClassName() { return className; }
        public void setClassName(String className) { this.className = className; }
        
        public String getJspFile() { return jspFile; }
        public void setJspFile(String jspFile) { this.jspFile = jspFile; }
        
        public Map<String, String> getInitParams() { return initParams; }
        public void addInitParam(String name, String value) { this.initParams.put(name, value); }
    }
    
    /**
     * Filter information container
     */
    public static class FilterInfo {
        private String name;
        private String className;
        private Map<String, String> initParams = new HashMap<>();
        
        // Getters and setters
        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
        
        public String getClassName() { return className; }
        public void setClassName(String className) { this.className = className; }
        
        public Map<String, String> getInitParams() { return initParams; }
        public void addInitParam(String name, String value) { this.initParams.put(name, value); }
    }
    
    /**
     * Web.xml metadata container
     */
    public static class WebXmlMetadata {
        private String version;
        private String displayName;
        private String description;
        private List<ServletInfo> servlets = new ArrayList<>();
        private Map<String, String> servletMappings = new HashMap<>();
        private List<FilterInfo> filters = new ArrayList<>();
        private Map<String, String> filterMappings = new HashMap<>();
        private List<String> listeners = new ArrayList<>();
        private Map<String, String> contextParams = new HashMap<>();
        private List<String> welcomeFiles = new ArrayList<>();
        private List<ErrorPageInfo> errorPages = new ArrayList<>();
        
        // Getters and setters
        public String getVersion() { return version; }
        public void setVersion(String version) { this.version = version; }
        
        public String getDisplayName() { return displayName; }
        public void setDisplayName(String displayName) { this.displayName = displayName; }
        
        public String getDescription() { return description; }
        public void setDescription(String description) { this.description = description; }
        
        public List<ServletInfo> getServlets() { return servlets; }
        public void addServlet(ServletInfo servlet) { this.servlets.add(servlet); }
        
        public Map<String, String> getServletMappings() { return servletMappings; }
        public void addServletMapping(String servletName, String urlPattern) { 
            this.servletMappings.put(servletName, urlPattern); 
        }
        
        public List<FilterInfo> getFilters() { return filters; }
        public void addFilter(FilterInfo filter) { this.filters.add(filter); }
        
        public Map<String, String> getFilterMappings() { return filterMappings; }
        public void addFilterMapping(String filterName, String urlPattern, String servletName) {
            String mapping = urlPattern != null ? urlPattern : servletName;
            this.filterMappings.put(filterName, mapping);
        }
        
        public List<String> getListeners() { return listeners; }
        public void addListener(String listener) { this.listeners.add(listener); }
        
        public Map<String, String> getContextParams() { return contextParams; }
        public void addContextParam(String name, String value) { this.contextParams.put(name, value); }
        
        public List<String> getWelcomeFiles() { return welcomeFiles; }
        public void addWelcomeFile(String welcomeFile) { this.welcomeFiles.add(welcomeFile); }
        
        public List<ErrorPageInfo> getErrorPages() { return errorPages; }
        public void addErrorPage(String errorCode, String exceptionType, String location) {
            this.errorPages.add(new ErrorPageInfo(errorCode, exceptionType, location));
        }
        
        public Map<String, Object> toMap() {
            Map<String, Object> map = new HashMap<>();
            if (version != null) map.put("version", version);
            if (displayName != null) map.put("displayName", displayName);
            if (description != null) map.put("description", description);
            map.put("servlets", servlets);
            map.put("servletMappings", servletMappings);
            map.put("filters", filters);
            map.put("filterMappings", filterMappings);
            map.put("listeners", listeners);
            map.put("contextParams", contextParams);
            map.put("welcomeFiles", welcomeFiles);
            map.put("errorPages", errorPages);
            return map;
        }
    }
    
    /**
     * Error page information container
     */
    public static class ErrorPageInfo {
        private String errorCode;
        private String exceptionType;
        private String location;
        
        public ErrorPageInfo(String errorCode, String exceptionType, String location) {
            this.errorCode = errorCode;
            this.exceptionType = exceptionType;
            this.location = location;
        }
        
        // Getters
        public String getErrorCode() { return errorCode; }
        public String getExceptionType() { return exceptionType; }
        public String getLocation() { return location; }
    }
}
