package com.phodal.legacy.model;

import java.util.*;

/**
 * Represents a node in the dependency graph for legacy JSP application components.
 * Each node contains a component and its relationships to other components.
 */
public class GraphNode {
    
    private ComponentModel component;
    private Set<GraphNode> dependencies;
    private Set<GraphNode> dependents;
    private Map<String, Object> nodeProperties;
    private int depth;
    private boolean visited;
    
    public GraphNode(ComponentModel component) {
        this.component = component;
        this.dependencies = new HashSet<>();
        this.dependents = new HashSet<>();
        this.nodeProperties = new HashMap<>();
        this.depth = -1;
        this.visited = false;
    }
    
    // Basic getters and setters
    public ComponentModel getComponent() { return component; }
    public void setComponent(ComponentModel component) { this.component = component; }
    
    public Set<GraphNode> getDependencies() { return dependencies; }
    public void setDependencies(Set<GraphNode> dependencies) { this.dependencies = dependencies; }
    
    public Set<GraphNode> getDependents() { return dependents; }
    public void setDependents(Set<GraphNode> dependents) { this.dependents = dependents; }
    
    public Map<String, Object> getNodeProperties() { return nodeProperties; }
    public void setNodeProperties(Map<String, Object> nodeProperties) { this.nodeProperties = nodeProperties; }
    
    public int getDepth() { return depth; }
    public void setDepth(int depth) { this.depth = depth; }
    
    public boolean isVisited() { return visited; }
    public void setVisited(boolean visited) { this.visited = visited; }
    
    // Utility methods
    public void addDependency(GraphNode dependency) {
        this.dependencies.add(dependency);
        dependency.dependents.add(this);
    }
    
    public void removeDependency(GraphNode dependency) {
        this.dependencies.remove(dependency);
        dependency.dependents.remove(this);
    }
    
    public void addDependent(GraphNode dependent) {
        this.dependents.add(dependent);
        dependent.dependencies.add(this);
    }
    
    public void removeDependent(GraphNode dependent) {
        this.dependents.remove(dependent);
        dependent.dependencies.remove(this);
    }
    
    public void addNodeProperty(String key, Object value) {
        this.nodeProperties.put(key, value);
    }
    
    public Object getNodeProperty(String key) {
        return this.nodeProperties.get(key);
    }
    
    /**
     * Check if this node has any dependencies
     */
    public boolean hasDependencies() {
        return !dependencies.isEmpty();
    }
    
    /**
     * Check if this node has any dependents
     */
    public boolean hasDependents() {
        return !dependents.isEmpty();
    }
    
    /**
     * Check if this node is a leaf node (no dependents)
     */
    public boolean isLeaf() {
        return dependents.isEmpty();
    }
    
    /**
     * Check if this node is a root node (no dependencies)
     */
    public boolean isRoot() {
        return dependencies.isEmpty();
    }
    
    /**
     * Get all transitive dependencies (dependencies of dependencies)
     */
    public Set<GraphNode> getTransitiveDependencies() {
        Set<GraphNode> transitive = new HashSet<>();
        Set<GraphNode> visited = new HashSet<>();
        collectTransitiveDependencies(this, transitive, visited);
        transitive.remove(this); // Remove self
        return transitive;
    }
    
    /**
     * Get all transitive dependents (dependents of dependents)
     */
    public Set<GraphNode> getTransitiveDependents() {
        Set<GraphNode> transitive = new HashSet<>();
        Set<GraphNode> visited = new HashSet<>();
        collectTransitiveDependents(this, transitive, visited);
        transitive.remove(this); // Remove self
        return transitive;
    }
    
    /**
     * Check if this node depends on another node (directly or transitively)
     */
    public boolean dependsOn(GraphNode other) {
        if (dependencies.contains(other)) {
            return true;
        }
        
        Set<GraphNode> visited = new HashSet<>();
        return dependsOnRecursive(other, visited);
    }
    
    /**
     * Check if this node is depended on by another node (directly or transitively)
     */
    public boolean isDependedOnBy(GraphNode other) {
        return other.dependsOn(this);
    }
    
    /**
     * Calculate the migration priority based on dependencies and complexity
     */
    public int calculateMigrationPriority() {
        int priority = 0;
        
        // Base priority on number of dependents (more dependents = higher priority)
        priority += dependents.size() * 10;
        
        // Add priority based on component type
        switch (component.getType()) {
            case WEB_XML:
                priority += 100; // Highest priority
                break;
            case SERVLET:
                priority += 80;
                break;
            case FILTER:
                priority += 70;
                break;
            case LISTENER:
                priority += 60;
                break;
            case JAVA_CLASS:
                priority += 50;
                break;
            case JSP_PAGE:
                priority += 30;
                break;
            case STATIC_RESOURCE:
                priority += 10;
                break;
            default:
                priority += 20;
        }
        
        // Reduce priority based on complexity (more complex = lower priority initially)
        Object complexity = component.getProperty("complexity");
        if (complexity instanceof Integer) {
            priority -= ((Integer) complexity) * 2;
        }
        
        return Math.max(priority, 1); // Ensure minimum priority of 1
    }
    
    /**
     * Get component ID for easy identification
     */
    public String getId() {
        return component.getId();
    }
    
    /**
     * Get component name for display
     */
    public String getName() {
        return component.getName();
    }
    
    /**
     * Get component type
     */
    public ComponentModel.ComponentType getType() {
        return component.getType();
    }
    
    // Private helper methods
    private void collectTransitiveDependencies(GraphNode node, Set<GraphNode> transitive, Set<GraphNode> visited) {
        if (visited.contains(node)) {
            return; // Avoid cycles
        }
        
        visited.add(node);
        transitive.add(node);
        
        for (GraphNode dependency : node.dependencies) {
            collectTransitiveDependencies(dependency, transitive, visited);
        }
    }
    
    private void collectTransitiveDependents(GraphNode node, Set<GraphNode> transitive, Set<GraphNode> visited) {
        if (visited.contains(node)) {
            return; // Avoid cycles
        }
        
        visited.add(node);
        transitive.add(node);
        
        for (GraphNode dependent : node.dependents) {
            collectTransitiveDependents(dependent, transitive, visited);
        }
    }
    
    private boolean dependsOnRecursive(GraphNode target, Set<GraphNode> visited) {
        if (visited.contains(this)) {
            return false; // Avoid cycles
        }
        
        visited.add(this);
        
        for (GraphNode dependency : dependencies) {
            if (dependency.equals(target) || dependency.dependsOnRecursive(target, visited)) {
                return true;
            }
        }
        
        return false;
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        GraphNode graphNode = (GraphNode) o;
        return Objects.equals(component.getId(), graphNode.component.getId());
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(component.getId());
    }
    
    @Override
    public String toString() {
        return String.format("GraphNode{id='%s', type=%s, dependencies=%d, dependents=%d}", 
            component.getId(), component.getType(), dependencies.size(), dependents.size());
    }
}
