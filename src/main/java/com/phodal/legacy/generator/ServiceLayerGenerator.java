package com.phodal.legacy.generator;

import com.phodal.legacy.model.ComponentModel;
import com.squareup.javapoet.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.lang.model.element.Modifier;
import java.io.IOException;
import java.nio.file.Path;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Generates service layer classes for business logic.
 */
public class ServiceLayerGenerator {
    
    private static final Logger logger = LoggerFactory.getLogger(ServiceLayerGenerator.class);
    
    private final GenerationConfig config;
    
    public ServiceLayerGenerator(GenerationConfig config) {
        this.config = config;
    }
    
    /**
     * Generate service classes from Java components and entities
     */
    public List<GeneratedClass> generateServices(List<ComponentModel> javaComponents,
                                               List<GeneratedClass> entities,
                                               Path targetPath) throws IOException {
        
        List<GeneratedClass> services = new ArrayList<>();
        
        // Generate services for each entity
        for (GeneratedClass entity : entities) {
            GeneratedClass service = generateServiceForEntity(entity, targetPath);
            services.add(service);
        }
        
        // Generate services for existing Java components that look like services
        List<ComponentModel> serviceCandidates = identifyServiceCandidates(javaComponents);
        for (ComponentModel candidate : serviceCandidates) {
            GeneratedClass service = generateServiceFromComponent(candidate, targetPath);
            services.add(service);
        }
        
        logger.info("Generated {} service classes", services.size());
        return services;
    }
    
    private List<ComponentModel> identifyServiceCandidates(List<ComponentModel> javaComponents) {
        return javaComponents.stream()
            .filter(this::isLikelyService)
            .collect(Collectors.toList());
    }
    
    private boolean isLikelyService(ComponentModel component) {
        String name = component.getName().toLowerCase();
        
        return name.contains("service") ||
               name.contains("manager") ||
               name.contains("handler") ||
               name.contains("processor") ||
               name.contains("business") ||
               name.contains("logic") ||
               // Check for service-like patterns
               hasServiceCharacteristics(component);
    }
    
    private boolean hasServiceCharacteristics(ComponentModel component) {
        // Check if component has characteristics of a service
        return component.getDependencies().stream().anyMatch(dep ->
            dep.contains("repository") ||
            dep.contains("dao") ||
            dep.contains("service") ||
            dep.contains("javax.transaction") ||
            dep.contains("org.springframework.transaction"));
    }
    
    private GeneratedClass generateServiceForEntity(GeneratedClass entity, Path targetPath) throws IOException {
        String className = entity.getClassName() + "Service";
        String packageName = config.getBasePackage() + ".service";
        String repositoryClassName = entity.getClassName() + "Repository";
        
        TypeSpec.Builder serviceClass = TypeSpec.classBuilder(className)
            .addModifiers(Modifier.PUBLIC)
            .addAnnotation(ClassName.get("org.springframework.stereotype", "Service"))
            .addAnnotation(ClassName.get("org.springframework.transaction.annotation", "Transactional"));
        
        // Add repository dependency
        String repositoryFieldName = Character.toLowerCase(repositoryClassName.charAt(0)) + 
                                    repositoryClassName.substring(1);
        
        serviceClass.addField(FieldSpec.builder(
            ClassName.get(config.getBasePackage() + ".repository", repositoryClassName),
            repositoryFieldName,
            Modifier.PRIVATE, Modifier.FINAL)
            .build());
        
        // Constructor
        serviceClass.addMethod(MethodSpec.constructorBuilder()
            .addModifiers(Modifier.PUBLIC)
            .addParameter(ClassName.get(config.getBasePackage() + ".repository", repositoryClassName), repositoryFieldName)
            .addStatement("this.$N = $N", repositoryFieldName, repositoryFieldName)
            .build());
        
        // Add CRUD methods
        addCrudMethods(serviceClass, entity, repositoryFieldName);
        
        JavaFile javaFile = JavaFile.builder(packageName, serviceClass.build())
            .addFileComment("Generated service class for entity: " + entity.getClassName())
            .build();
        
        Path javaPath = targetPath.resolve("src/main/java");
        javaFile.writeTo(javaPath);
        
        GeneratedClass generatedClass = new GeneratedClass();
        generatedClass.setClassName(className);
        generatedClass.setPackageName(packageName);
        generatedClass.setFilePath(javaPath.resolve(packageName.replace(".", "/")).resolve(className + ".java"));
        generatedClass.setType(GeneratedClass.ClassType.SERVICE);
        generatedClass.setSourceComponent(entity.getClassName());
        
        logger.info("Generated service: {}.{}", packageName, className);
        return generatedClass;
    }
    
    private void addCrudMethods(TypeSpec.Builder serviceClass, GeneratedClass entity, String repositoryFieldName) {
        String entityClassName = entity.getClassName();
        ClassName entityClass = ClassName.get(entity.getPackageName(), entityClassName);
        
        // findAll method
        serviceClass.addMethod(MethodSpec.methodBuilder("findAll")
            .addModifiers(Modifier.PUBLIC)
            .returns(ParameterizedTypeName.get(ClassName.get(List.class), entityClass))
            .addStatement("return $N.findAll()", repositoryFieldName)
            .build());
        
        // findById method
        serviceClass.addMethod(MethodSpec.methodBuilder("findById")
            .addModifiers(Modifier.PUBLIC)
            .returns(ParameterizedTypeName.get(ClassName.get(Optional.class), entityClass))
            .addParameter(Long.class, "id")
            .addStatement("return $N.findById(id)", repositoryFieldName)
            .build());
        
        // save method
        serviceClass.addMethod(MethodSpec.methodBuilder("save")
            .addModifiers(Modifier.PUBLIC)
            .returns(entityClass)
            .addParameter(entityClass, entityClassName.toLowerCase())
            .addStatement("return $N.save($N)", repositoryFieldName, entityClassName.toLowerCase())
            .build());
        
        // update method
        serviceClass.addMethod(MethodSpec.methodBuilder("update")
            .addModifiers(Modifier.PUBLIC)
            .returns(entityClass)
            .addParameter(Long.class, "id")
            .addParameter(entityClass, entityClassName.toLowerCase())
            .addStatement("$N.setId(id)", entityClassName.toLowerCase())
            .addStatement("return $N.save($N)", repositoryFieldName, entityClassName.toLowerCase())
            .build());
        
        // deleteById method
        serviceClass.addMethod(MethodSpec.methodBuilder("deleteById")
            .addModifiers(Modifier.PUBLIC)
            .returns(void.class)
            .addParameter(Long.class, "id")
            .addStatement("$N.deleteById(id)", repositoryFieldName)
            .build());
        
        // Add entity-specific methods
        if (entityClassName.equals("Post")) {
            serviceClass.addMethod(MethodSpec.methodBuilder("findByTitleContaining")
                .addModifiers(Modifier.PUBLIC)
                .returns(ParameterizedTypeName.get(ClassName.get(List.class), entityClass))
                .addParameter(String.class, "title")
                .addStatement("return $N.findByTitleContaining(title)", repositoryFieldName)
                .build());
        }
    }
    
    private GeneratedClass generateServiceFromComponent(ComponentModel component, Path targetPath) throws IOException {
        String className = extractServiceName(component.getName());
        String packageName = config.getBasePackage() + ".service";
        
        TypeSpec.Builder serviceClass = TypeSpec.classBuilder(className)
            .addModifiers(Modifier.PUBLIC)
            .addAnnotation(ClassName.get("org.springframework.stereotype", "Service"));
        
        // Add basic business logic method
        serviceClass.addMethod(MethodSpec.methodBuilder("processBusinessLogic")
            .addModifiers(Modifier.PUBLIC)
            .returns(String.class)
            .addStatement("// TODO: Implement business logic from $N", component.getName())
            .addStatement("return $S", "Business logic processed")
            .build());
        
        JavaFile javaFile = JavaFile.builder(packageName, serviceClass.build())
            .addFileComment("Generated service class from component: " + component.getName())
            .build();
        
        Path javaPath = targetPath.resolve("src/main/java");
        javaFile.writeTo(javaPath);
        
        GeneratedClass generatedClass = new GeneratedClass();
        generatedClass.setClassName(className);
        generatedClass.setPackageName(packageName);
        generatedClass.setFilePath(javaPath.resolve(packageName.replace(".", "/")).resolve(className + ".java"));
        generatedClass.setType(GeneratedClass.ClassType.SERVICE);
        generatedClass.setSourceComponent(component.getName());
        
        logger.info("Generated service from component: {}.{}", packageName, className);
        return generatedClass;
    }
    
    private String extractServiceName(String componentName) {
        String name = componentName;

        // Remove .java extension if present
        if (name.endsWith(".java")) {
            name = name.substring(0, name.length() - 5);
        }

        // Remove common suffixes and add Service if not present
        name = name.replaceAll("(?i)(impl|implementation)$", "");

        if (!name.toLowerCase().endsWith("service")) {
            name += "Service";
        }

        // Capitalize first letter
        if (!name.isEmpty()) {
            name = Character.toUpperCase(name.charAt(0)) + name.substring(1);
        }

        return name;
    }
}
