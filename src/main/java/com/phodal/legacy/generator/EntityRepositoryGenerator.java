package com.phodal.legacy.generator;

import com.phodal.legacy.model.ComponentModel;
import com.squareup.javapoet.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.lang.model.element.Modifier;
import java.io.IOException;
import java.nio.file.Path;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Generates JPA entities and repositories from Java components.
 */
public class EntityRepositoryGenerator {
    
    private static final Logger logger = LoggerFactory.getLogger(EntityRepositoryGenerator.class);
    
    private final GenerationConfig config;
    
    public EntityRepositoryGenerator(GenerationConfig config) {
        this.config = config;
    }
    
    /**
     * Generate JPA entities from Java components
     */
    public List<GeneratedClass> generateEntities(List<ComponentModel> javaComponents, Path targetPath) throws IOException {
        List<GeneratedClass> entities = new ArrayList<>();
        
        // Identify potential entity classes (models, POJOs, etc.)
        List<ComponentModel> entityCandidates = identifyEntityCandidates(javaComponents);
        
        for (ComponentModel candidate : entityCandidates) {
            GeneratedClass entity = generateEntity(candidate, targetPath);
            entities.add(entity);
        }
        
        // Generate a default Post entity if no entities were found
        if (entities.isEmpty()) {
            GeneratedClass defaultEntity = generateDefaultPostEntity(targetPath);
            entities.add(defaultEntity);
        }
        
        logger.info("Generated {} JPA entities", entities.size());
        return entities;
    }
    
    /**
     * Generate Spring Data repositories for entities
     */
    public List<GeneratedClass> generateRepositories(List<GeneratedClass> entities, Path targetPath) throws IOException {
        List<GeneratedClass> repositories = new ArrayList<>();
        
        for (GeneratedClass entity : entities) {
            GeneratedClass repository = generateRepository(entity, targetPath);
            repositories.add(repository);
        }
        
        logger.info("Generated {} repositories", repositories.size());
        return repositories;
    }
    
    private List<ComponentModel> identifyEntityCandidates(List<ComponentModel> javaComponents) {
        return javaComponents.stream()
            .filter(this::isLikelyEntity)
            .collect(Collectors.toList());
    }
    
    private boolean isLikelyEntity(ComponentModel component) {
        String name = component.getName().toLowerCase();

        // Exclude repository, service, controller, test classes
        if (name.contains("repository") ||
            name.contains("service") ||
            name.contains("controller") ||
            name.contains("test") ||
            name.contains("strategy") ||
            name.contains("handler") ||
            name.contains("exception")) {
            return false;
        }

        // Check if it's likely a model/entity class
        return name.contains("model") ||
               name.contains("entity") ||
               name.contains("pojo") ||
               name.contains("bean") ||
               name.equals("post.java") ||
               name.equals("user.java") ||
               name.equals("blog.java") ||
               // Check if it has typical entity characteristics
               hasEntityCharacteristics(component);
    }
    
    private boolean hasEntityCharacteristics(ComponentModel component) {
        // Check if component has properties that suggest it's an entity
        Map<String, Object> properties = component.getProperties();
        
        // Look for typical entity patterns
        return properties.containsKey("fields") ||
               properties.containsKey("properties") ||
               component.getDependencies().stream().anyMatch(dep -> 
                   dep.contains("javax.persistence") || 
                   dep.contains("jakarta.persistence") ||
                   dep.contains("java.sql"));
    }
    
    private GeneratedClass generateEntity(ComponentModel component, Path targetPath) throws IOException {
        String className = extractEntityName(component.getName());
        String packageName = config.getBasePackage() + ".entity";
        
        TypeSpec.Builder entityClass = TypeSpec.classBuilder(className)
            .addModifiers(Modifier.PUBLIC)
            .addAnnotation(ClassName.get("jakarta.persistence", "Entity"))
            .addAnnotation(AnnotationSpec.builder(ClassName.get("jakarta.persistence", "Table"))
                .addMember("name", "$S", className.toLowerCase() + "s")
                .build());
        
        // Add ID field
        entityClass.addField(FieldSpec.builder(Long.class, "id", Modifier.PRIVATE)
            .addAnnotation(ClassName.get("jakarta.persistence", "Id"))
            .addAnnotation(ClassName.get("jakarta.persistence", "GeneratedValue"))
            .build());
        
        // Add common fields based on component analysis
        addEntityFields(entityClass, component);
        
        // Add getters and setters
        if (className.equals("Post")) {
            addGettersAndSettersForPost(entityClass);
        } else {
            addGettersAndSetters(entityClass, className);
        }
        
        JavaFile javaFile = JavaFile.builder(packageName, entityClass.build())
            .addFileComment("Generated JPA entity from component: " + component.getName())
            .build();
        
        Path javaPath = targetPath.resolve("src/main/java");
        javaFile.writeTo(javaPath);
        
        GeneratedClass generatedClass = new GeneratedClass();
        generatedClass.setClassName(className);
        generatedClass.setPackageName(packageName);
        generatedClass.setFilePath(javaPath.resolve(packageName.replace(".", "/")).resolve(className + ".java"));
        generatedClass.setType(GeneratedClass.ClassType.ENTITY);
        generatedClass.setSourceComponent(component.getName());
        
        logger.info("Generated JPA entity: {}.{}", packageName, className);
        return generatedClass;
    }
    
    private GeneratedClass generateDefaultPostEntity(Path targetPath) throws IOException {
        String className = "Post";
        String packageName = config.getBasePackage() + ".entity";
        
        TypeSpec.Builder entityClass = TypeSpec.classBuilder(className)
            .addModifiers(Modifier.PUBLIC)
            .addAnnotation(ClassName.get("jakarta.persistence", "Entity"))
            .addAnnotation(AnnotationSpec.builder(ClassName.get("jakarta.persistence", "Table"))
                .addMember("name", "$S", "posts")
                .build());
        
        // Add fields
        entityClass.addField(FieldSpec.builder(Long.class, "id", Modifier.PRIVATE)
            .addAnnotation(ClassName.get("jakarta.persistence", "Id"))
            .addAnnotation(ClassName.get("jakarta.persistence", "GeneratedValue"))
            .build());
        
        entityClass.addField(FieldSpec.builder(String.class, "title", Modifier.PRIVATE)
            .addAnnotation(AnnotationSpec.builder(ClassName.get("jakarta.persistence", "Column"))
                .addMember("nullable", "false")
                .build())
            .build());
        
        entityClass.addField(FieldSpec.builder(String.class, "content", Modifier.PRIVATE)
            .addAnnotation(AnnotationSpec.builder(ClassName.get("jakarta.persistence", "Column"))
                .addMember("columnDefinition", "$S", "TEXT")
                .build())
            .build());
        
        entityClass.addField(FieldSpec.builder(ClassName.get("java.time", "LocalDateTime"), "createdAt", Modifier.PRIVATE)
            .addAnnotation(AnnotationSpec.builder(ClassName.get("jakarta.persistence", "Column"))
                .addMember("name", "$S", "created_at")
                .build())
            .build());
        
        entityClass.addField(FieldSpec.builder(ClassName.get("java.time", "LocalDateTime"), "updatedAt", Modifier.PRIVATE)
            .addAnnotation(AnnotationSpec.builder(ClassName.get("jakarta.persistence", "Column"))
                .addMember("name", "$S", "updated_at")
                .build())
            .build());
        
        // Add getters and setters
        addGettersAndSettersForPost(entityClass);
        
        JavaFile javaFile = JavaFile.builder(packageName, entityClass.build())
            .addFileComment("Generated default Post entity")
            .build();
        
        Path javaPath = targetPath.resolve("src/main/java");
        javaFile.writeTo(javaPath);
        
        GeneratedClass generatedClass = new GeneratedClass();
        generatedClass.setClassName(className);
        generatedClass.setPackageName(packageName);
        generatedClass.setFilePath(javaPath.resolve(packageName.replace(".", "/")).resolve(className + ".java"));
        generatedClass.setType(GeneratedClass.ClassType.ENTITY);
        generatedClass.setSourceComponent("default");
        
        logger.info("Generated default JPA entity: {}.{}", packageName, className);
        return generatedClass;
    }
    
    private String extractEntityName(String componentName) {
        // Extract entity name from component name
        String name = componentName;

        // Remove .java extension if present
        if (name.endsWith(".java")) {
            name = name.substring(0, name.length() - 5);
        }

        // Remove common suffixes
        name = name.replaceAll("(?i)(model|entity|pojo|bean)$", "");

        // Capitalize first letter
        if (!name.isEmpty()) {
            name = Character.toUpperCase(name.charAt(0)) + name.substring(1);
        }

        // Default to "Post" if name is empty or too generic
        if (name.isEmpty() || name.length() < 2) {
            name = "Post";
        }

        return name;
    }
    
    private void addEntityFields(TypeSpec.Builder entityClass, ComponentModel component) {
        // Add basic fields based on component analysis
        // This is a simplified implementation - in a real scenario, you'd analyze the component more thoroughly

        String componentName = component.getName().toLowerCase();
        if (componentName.contains("post") || componentName.equals("post.java")) {
            entityClass.addField(FieldSpec.builder(String.class, "title", Modifier.PRIVATE).build());
            entityClass.addField(FieldSpec.builder(String.class, "content", Modifier.PRIVATE).build());
        } else if (componentName.contains("user")) {
            entityClass.addField(FieldSpec.builder(String.class, "username", Modifier.PRIVATE).build());
            entityClass.addField(FieldSpec.builder(String.class, "email", Modifier.PRIVATE).build());
        } else {
            // Generic fields
            entityClass.addField(FieldSpec.builder(String.class, "name", Modifier.PRIVATE).build());
            entityClass.addField(FieldSpec.builder(String.class, "description", Modifier.PRIVATE).build());
        }

        // Add timestamp fields
        entityClass.addField(FieldSpec.builder(ClassName.get("java.time", "LocalDateTime"), "createdAt", Modifier.PRIVATE).build());
        entityClass.addField(FieldSpec.builder(ClassName.get("java.time", "LocalDateTime"), "updatedAt", Modifier.PRIVATE).build());
    }

    private void addGettersAndSetters(TypeSpec.Builder entityClass, String className) {
        // Add getter and setter for ID
        entityClass.addMethod(MethodSpec.methodBuilder("getId")
            .addModifiers(Modifier.PUBLIC)
            .returns(Long.class)
            .addStatement("return id")
            .build());

        entityClass.addMethod(MethodSpec.methodBuilder("setId")
            .addModifiers(Modifier.PUBLIC)
            .addParameter(Long.class, "id")
            .addStatement("this.id = id")
            .build());

        // Add getters and setters for common fields
        addStringFieldAccessors(entityClass, "name");
        addStringFieldAccessors(entityClass, "description");
        addDateTimeFieldAccessors(entityClass, "createdAt");
        addDateTimeFieldAccessors(entityClass, "updatedAt");
    }

    private void addGettersAndSettersForPost(TypeSpec.Builder entityClass) {
        // Add getter and setter for ID
        entityClass.addMethod(MethodSpec.methodBuilder("getId")
            .addModifiers(Modifier.PUBLIC)
            .returns(Long.class)
            .addStatement("return id")
            .build());

        entityClass.addMethod(MethodSpec.methodBuilder("setId")
            .addModifiers(Modifier.PUBLIC)
            .addParameter(Long.class, "id")
            .addStatement("this.id = id")
            .build());

        // Add getters and setters for Post-specific fields
        addStringFieldAccessors(entityClass, "title");
        addStringFieldAccessors(entityClass, "content");
        addDateTimeFieldAccessors(entityClass, "createdAt");
        addDateTimeFieldAccessors(entityClass, "updatedAt");
    }

    private void addStringFieldAccessors(TypeSpec.Builder entityClass, String fieldName) {
        String capitalizedName = Character.toUpperCase(fieldName.charAt(0)) + fieldName.substring(1);

        entityClass.addMethod(MethodSpec.methodBuilder("get" + capitalizedName)
            .addModifiers(Modifier.PUBLIC)
            .returns(String.class)
            .addStatement("return $N", fieldName)
            .build());

        entityClass.addMethod(MethodSpec.methodBuilder("set" + capitalizedName)
            .addModifiers(Modifier.PUBLIC)
            .addParameter(String.class, fieldName)
            .addStatement("this.$N = $N", fieldName, fieldName)
            .build());
    }

    private void addDateTimeFieldAccessors(TypeSpec.Builder entityClass, String fieldName) {
        String capitalizedName = Character.toUpperCase(fieldName.charAt(0)) + fieldName.substring(1);

        entityClass.addMethod(MethodSpec.methodBuilder("get" + capitalizedName)
            .addModifiers(Modifier.PUBLIC)
            .returns(ClassName.get("java.time", "LocalDateTime"))
            .addStatement("return $N", fieldName)
            .build());

        entityClass.addMethod(MethodSpec.methodBuilder("set" + capitalizedName)
            .addModifiers(Modifier.PUBLIC)
            .addParameter(ClassName.get("java.time", "LocalDateTime"), fieldName)
            .addStatement("this.$N = $N", fieldName, fieldName)
            .build());
    }

    private GeneratedClass generateRepository(GeneratedClass entity, Path targetPath) throws IOException {
        String className = entity.getClassName() + "Repository";
        String packageName = config.getBasePackage() + ".repository";

        TypeSpec.Builder repositoryInterface = TypeSpec.interfaceBuilder(className)
            .addModifiers(Modifier.PUBLIC)
            .addSuperinterface(ParameterizedTypeName.get(
                ClassName.get("org.springframework.data.jpa.repository", "JpaRepository"),
                ClassName.get(entity.getPackageName(), entity.getClassName()),
                ClassName.get(Long.class)));

        // Add custom query methods if needed
        if (entity.getClassName().equals("Post")) {
            repositoryInterface.addMethod(MethodSpec.methodBuilder("findByTitleContaining")
                .addModifiers(Modifier.PUBLIC, Modifier.ABSTRACT)
                .returns(ParameterizedTypeName.get(
                    ClassName.get(List.class),
                    ClassName.get(entity.getPackageName(), entity.getClassName())))
                .addParameter(String.class, "title")
                .build());
        }

        JavaFile javaFile = JavaFile.builder(packageName, repositoryInterface.build())
            .addFileComment("Generated Spring Data repository for entity: " + entity.getClassName())
            .build();

        Path javaPath = targetPath.resolve("src/main/java");
        javaFile.writeTo(javaPath);

        GeneratedClass generatedClass = new GeneratedClass();
        generatedClass.setClassName(className);
        generatedClass.setPackageName(packageName);
        generatedClass.setFilePath(javaPath.resolve(packageName.replace(".", "/")).resolve(className + ".java"));
        generatedClass.setType(GeneratedClass.ClassType.REPOSITORY);
        generatedClass.setSourceComponent(entity.getClassName());

        logger.info("Generated repository: {}.{}", packageName, className);
        return generatedClass;
    }
}
