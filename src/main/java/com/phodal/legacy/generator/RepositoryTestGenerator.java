package com.phodal.legacy.generator;

import com.squareup.javapoet.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.lang.model.element.Modifier;
import java.io.IOException;
import java.nio.file.Path;
import java.util.*;

/**
 * Generates tests for Spring Data JPA repositories using @DataJpaTest.
 */
public class RepositoryTestGenerator {
    
    private static final Logger logger = LoggerFactory.getLogger(RepositoryTestGenerator.class);
    
    private final GenerationConfig config;
    
    public RepositoryTestGenerator(GenerationConfig config) {
        this.config = config;
    }
    
    /**
     * Generate tests for all repository classes
     */
    public List<GeneratedClass> generateRepositoryTests(List<GeneratedClass> repositories,
                                                       List<GeneratedClass> entities,
                                                       Path targetPath) throws IOException {
        
        List<GeneratedClass> testClasses = new ArrayList<>();
        
        for (GeneratedClass repository : repositories) {
            Optional<GeneratedClass> entityOpt = findCorrespondingEntity(repository, entities);
            if (entityOpt.isPresent()) {
                GeneratedClass testClass = generateRepositoryTest(repository, entityOpt.get(), targetPath);
                testClasses.add(testClass);
            }
        }
        
        logger.info("Generated {} repository test classes", testClasses.size());
        return testClasses;
    }
    
    private GeneratedClass generateRepositoryTest(GeneratedClass repository,
                                                GeneratedClass entity,
                                                Path targetPath) throws IOException {
        
        String testClassName = repository.getClassName() + "Test";
        String testPackageName = config.getBasePackage() + ".repository";
        String entityName = entity.getClassName();
        String entityVarName = entityName.toLowerCase();
        
        TypeSpec.Builder testClass = TypeSpec.classBuilder(testClassName)
            .addModifiers(Modifier.PUBLIC)
            .addAnnotation(ClassName.get("org.springframework.boot.test.autoconfigure.orm.jpa", "DataJpaTest"))
            .addAnnotation(ClassName.get("org.springframework.test.context", "ActiveProfiles"))
            .addAnnotation(AnnotationSpec.builder(ClassName.get("org.springframework.test.context", "ActiveProfiles"))
                .addMember("value", "$S", "test")
                .build())
            .addJavadoc("Integration tests for $L using @DataJpaTest", repository.getClassName());
        
        // Add repository under test field
        testClass.addField(FieldSpec.builder(
            ClassName.get(repository.getPackageName(), repository.getClassName()),
            getRepositoryFieldName(repository.getClassName()),
            Modifier.PRIVATE)
            .addAnnotation(ClassName.get("org.springframework.beans.factory.annotation", "Autowired"))
            .build());
        
        // Add TestEntityManager field
        testClass.addField(FieldSpec.builder(
            ClassName.get("org.springframework.boot.test.autoconfigure.orm.jpa", "TestEntityManager"),
            "entityManager",
            Modifier.PRIVATE)
            .addAnnotation(ClassName.get("org.springframework.beans.factory.annotation", "Autowired"))
            .build());
        
        // Generate test methods
        addRepositoryTestMethods(testClass, repository, entity);
        
        JavaFile javaFile = JavaFile.builder(testPackageName, testClass.build())
            .addFileComment("Generated integration tests for " + repository.getClassName())
            .build();
        
        Path javaPath = targetPath.resolve("src/test/java");
        javaFile.writeTo(javaPath);
        
        GeneratedClass generatedClass = new GeneratedClass();
        generatedClass.setClassName(testClassName);
        generatedClass.setPackageName(testPackageName);
        generatedClass.setFilePath(javaPath.resolve(testPackageName.replace(".", "/")).resolve(testClassName + ".java"));
        generatedClass.setType(GeneratedClass.ClassType.TEST);
        generatedClass.setSourceComponent(repository.getClassName());
        
        logger.info("Generated repository test: {}.{}", testPackageName, testClassName);
        return generatedClass;
    }
    
    private void addRepositoryTestMethods(TypeSpec.Builder testClass, GeneratedClass repository, GeneratedClass entity) {
        String entityName = entity.getClassName();
        String entityVarName = entityName.toLowerCase();
        String repositoryFieldName = getRepositoryFieldName(repository.getClassName());
        
        // Test save and findById
        addSaveAndFindByIdTest(testClass, repositoryFieldName, entityName, entityVarName);
        
        // Test findAll
        addFindAllTest(testClass, repositoryFieldName, entityName, entityVarName);
        
        // Test delete
        addDeleteTest(testClass, repositoryFieldName, entityName, entityVarName);
        
        // Test existsById
        addExistsByIdTest(testClass, repositoryFieldName, entityName, entityVarName);
        
        // Test count
        addCountTest(testClass, repositoryFieldName, entityName, entityVarName);
    }
    
    private void addSaveAndFindByIdTest(TypeSpec.Builder testClass, String repositoryFieldName, 
                                      String entityName, String entityVarName) {
        MethodSpec.Builder testMethod = MethodSpec.methodBuilder("testSaveAndFindById")
            .addModifiers(Modifier.PUBLIC)
            .addAnnotation(ClassName.get("org.junit.jupiter.api", "Test"))
            .addStatement("// Given")
            .addStatement("$T $L = new $T()", 
                ClassName.get(config.getBasePackage() + ".entity", entityName),
                entityVarName,
                ClassName.get(config.getBasePackage() + ".entity", entityName))
            .addStatement("// Set entity properties here if needed")
            .addStatement("")
            .addStatement("// When")
            .addStatement("$T saved$L = $L.save($L)", 
                ClassName.get(config.getBasePackage() + ".entity", entityName),
                entityName, repositoryFieldName, entityVarName)
            .addStatement("entityManager.flush()")
            .addStatement("entityManager.clear()")
            .addStatement("")
            .addStatement("$T<$T> found$L = $L.findById(saved$L.getId())", 
                ClassName.get(Optional.class),
                ClassName.get(config.getBasePackage() + ".entity", entityName),
                entityName, repositoryFieldName, entityName)
            .addStatement("")
            .addStatement("// Then")
            .addStatement("$T.assertNotNull(saved$L)", ClassName.get("org.junit.jupiter.api", "Assertions"), entityName)
            .addStatement("$T.assertNotNull(saved$L.getId())", ClassName.get("org.junit.jupiter.api", "Assertions"), entityName)
            .addStatement("$T.assertTrue(found$L.isPresent())", ClassName.get("org.junit.jupiter.api", "Assertions"), entityName)
            .addStatement("$T.assertEquals(saved$L.getId(), found$L.get().getId())", 
                ClassName.get("org.junit.jupiter.api", "Assertions"), entityName, entityName);
        
        testClass.addMethod(testMethod.build());
    }
    
    private void addFindAllTest(TypeSpec.Builder testClass, String repositoryFieldName, 
                              String entityName, String entityVarName) {
        MethodSpec.Builder testMethod = MethodSpec.methodBuilder("testFindAll")
            .addModifiers(Modifier.PUBLIC)
            .addAnnotation(ClassName.get("org.junit.jupiter.api", "Test"))
            .addStatement("// Given")
            .addStatement("$T $L1 = new $T()", 
                ClassName.get(config.getBasePackage() + ".entity", entityName),
                entityVarName,
                ClassName.get(config.getBasePackage() + ".entity", entityName))
            .addStatement("$T $L2 = new $T()", 
                ClassName.get(config.getBasePackage() + ".entity", entityName),
                entityVarName,
                ClassName.get(config.getBasePackage() + ".entity", entityName))
            .addStatement("$L.save($L1)", repositoryFieldName, entityVarName)
            .addStatement("$L.save($L2)", repositoryFieldName, entityVarName)
            .addStatement("entityManager.flush()")
            .addStatement("")
            .addStatement("// When")
            .addStatement("$T<$T> all$Ls = $L.findAll()", 
                ClassName.get(List.class),
                ClassName.get(config.getBasePackage() + ".entity", entityName),
                entityName, repositoryFieldName)
            .addStatement("")
            .addStatement("// Then")
            .addStatement("$T.assertNotNull(all$Ls)", ClassName.get("org.junit.jupiter.api", "Assertions"), entityName)
            .addStatement("$T.assertTrue(all$Ls.size() >= 2)", ClassName.get("org.junit.jupiter.api", "Assertions"), entityName);
        
        testClass.addMethod(testMethod.build());
    }
    
    private void addDeleteTest(TypeSpec.Builder testClass, String repositoryFieldName, 
                             String entityName, String entityVarName) {
        MethodSpec.Builder testMethod = MethodSpec.methodBuilder("testDelete")
            .addModifiers(Modifier.PUBLIC)
            .addAnnotation(ClassName.get("org.junit.jupiter.api", "Test"))
            .addStatement("// Given")
            .addStatement("$T $L = new $T()", 
                ClassName.get(config.getBasePackage() + ".entity", entityName),
                entityVarName,
                ClassName.get(config.getBasePackage() + ".entity", entityName))
            .addStatement("$T saved$L = $L.save($L)", 
                ClassName.get(config.getBasePackage() + ".entity", entityName),
                entityName, repositoryFieldName, entityVarName)
            .addStatement("entityManager.flush()")
            .addStatement("$T id = saved$L.getId()", Long.class, entityName)
            .addStatement("")
            .addStatement("// When")
            .addStatement("$L.deleteById(id)", repositoryFieldName)
            .addStatement("entityManager.flush()")
            .addStatement("")
            .addStatement("// Then")
            .addStatement("$T<$T> deleted$L = $L.findById(id)", 
                ClassName.get(Optional.class),
                ClassName.get(config.getBasePackage() + ".entity", entityName),
                entityName, repositoryFieldName)
            .addStatement("$T.assertFalse(deleted$L.isPresent())", ClassName.get("org.junit.jupiter.api", "Assertions"), entityName);
        
        testClass.addMethod(testMethod.build());
    }
    
    private void addExistsByIdTest(TypeSpec.Builder testClass, String repositoryFieldName, 
                                 String entityName, String entityVarName) {
        MethodSpec.Builder testMethod = MethodSpec.methodBuilder("testExistsById")
            .addModifiers(Modifier.PUBLIC)
            .addAnnotation(ClassName.get("org.junit.jupiter.api", "Test"))
            .addStatement("// Given")
            .addStatement("$T $L = new $T()", 
                ClassName.get(config.getBasePackage() + ".entity", entityName),
                entityVarName,
                ClassName.get(config.getBasePackage() + ".entity", entityName))
            .addStatement("$T saved$L = $L.save($L)", 
                ClassName.get(config.getBasePackage() + ".entity", entityName),
                entityName, repositoryFieldName, entityVarName)
            .addStatement("entityManager.flush()")
            .addStatement("")
            .addStatement("// When & Then")
            .addStatement("$T.assertTrue($L.existsById(saved$L.getId()))", 
                ClassName.get("org.junit.jupiter.api", "Assertions"), repositoryFieldName, entityName)
            .addStatement("$T.assertFalse($L.existsById(999L))", 
                ClassName.get("org.junit.jupiter.api", "Assertions"), repositoryFieldName);
        
        testClass.addMethod(testMethod.build());
    }
    
    private void addCountTest(TypeSpec.Builder testClass, String repositoryFieldName, 
                            String entityName, String entityVarName) {
        MethodSpec.Builder testMethod = MethodSpec.methodBuilder("testCount")
            .addModifiers(Modifier.PUBLIC)
            .addAnnotation(ClassName.get("org.junit.jupiter.api", "Test"))
            .addStatement("// Given")
            .addStatement("long initialCount = $L.count()", repositoryFieldName)
            .addStatement("$T $L = new $T()", 
                ClassName.get(config.getBasePackage() + ".entity", entityName),
                entityVarName,
                ClassName.get(config.getBasePackage() + ".entity", entityName))
            .addStatement("$L.save($L)", repositoryFieldName, entityVarName)
            .addStatement("entityManager.flush()")
            .addStatement("")
            .addStatement("// When")
            .addStatement("long newCount = $L.count()", repositoryFieldName)
            .addStatement("")
            .addStatement("// Then")
            .addStatement("$T.assertEquals(initialCount + 1, newCount)", ClassName.get("org.junit.jupiter.api", "Assertions"));
        
        testClass.addMethod(testMethod.build());
    }
    
    private Optional<GeneratedClass> findCorrespondingEntity(GeneratedClass repository, List<GeneratedClass> entities) {
        String repositoryName = repository.getClassName().replace("Repository", "");
        return entities.stream()
            .filter(e -> e.getClassName().toLowerCase().contains(repositoryName.toLowerCase()))
            .findFirst();
    }
    
    private String getRepositoryFieldName(String repositoryClassName) {
        return Character.toLowerCase(repositoryClassName.charAt(0)) + repositoryClassName.substring(1);
    }
}
