package com.phodal.legacy.generator;

import com.squareup.javapoet.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.lang.model.element.Modifier;
import java.io.IOException;
import java.nio.file.Path;
import java.util.*;

/**
 * Generates test data builders and fixtures for entities.
 */
public class TestDataGenerator {
    
    private static final Logger logger = LoggerFactory.getLogger(TestDataGenerator.class);
    
    private final GenerationConfig config;
    
    public TestDataGenerator(GenerationConfig config) {
        this.config = config;
    }
    
    /**
     * Generate test data builders and fixtures for all entities
     */
    public List<GeneratedClass> generateTestData(List<GeneratedClass> entities, Path targetPath) throws IOException {
        List<GeneratedClass> testDataClasses = new ArrayList<>();
        
        for (GeneratedClass entity : entities) {
            // Generate builder class
            GeneratedClass builderClass = generateTestDataBuilder(entity, targetPath);
            testDataClasses.add(builderClass);
            
            // Generate fixture class
            GeneratedClass fixtureClass = generateTestDataFixture(entity, targetPath);
            testDataClasses.add(fixtureClass);
        }
        
        // Generate test data factory
        GeneratedClass factoryClass = generateTestDataFactory(entities, targetPath);
        testDataClasses.add(factoryClass);
        
        logger.info("Generated {} test data classes", testDataClasses.size());
        return testDataClasses;
    }
    
    private GeneratedClass generateTestDataBuilder(GeneratedClass entity, Path targetPath) throws IOException {
        String entityName = entity.getClassName();
        String builderClassName = entityName + "TestDataBuilder";
        String testPackageName = config.getBasePackage() + ".testdata";
        
        TypeSpec.Builder builderClass = TypeSpec.classBuilder(builderClassName)
            .addModifiers(Modifier.PUBLIC)
            .addJavadoc("Test data builder for $L entity", entityName);
        
        // Add entity field
        builderClass.addField(FieldSpec.builder(
            ClassName.get(config.getBasePackage() + ".entity", entityName),
            "entity",
            Modifier.PRIVATE)
            .build());
        
        // Add constructor
        builderClass.addMethod(MethodSpec.constructorBuilder()
            .addModifiers(Modifier.PUBLIC)
            .addStatement("this.entity = new $T()", 
                ClassName.get(config.getBasePackage() + ".entity", entityName))
            .addStatement("// Set default values")
            .addStatement("withDefaultValues()")
            .build());
        
        // Add static factory method
        builderClass.addMethod(MethodSpec.methodBuilder("a" + entityName)
            .addModifiers(Modifier.PUBLIC, Modifier.STATIC)
            .returns(ClassName.get(testPackageName, builderClassName))
            .addStatement("return new $T()", ClassName.get(testPackageName, builderClassName))
            .build());
        
        // Add withDefaultValues method
        addWithDefaultValuesMethod(builderClass, entityName);
        
        // Add common builder methods
        addCommonBuilderMethods(builderClass, entityName, builderClassName, testPackageName);
        
        // Add build method
        builderClass.addMethod(MethodSpec.methodBuilder("build")
            .addModifiers(Modifier.PUBLIC)
            .returns(ClassName.get(config.getBasePackage() + ".entity", entityName))
            .addStatement("return entity")
            .build());
        
        JavaFile javaFile = JavaFile.builder(testPackageName, builderClass.build())
            .addFileComment("Generated test data builder for " + entityName)
            .build();
        
        Path javaPath = targetPath.resolve("src/test/java");
        javaFile.writeTo(javaPath);
        
        GeneratedClass generatedClass = new GeneratedClass();
        generatedClass.setClassName(builderClassName);
        generatedClass.setPackageName(testPackageName);
        generatedClass.setFilePath(javaPath.resolve(testPackageName.replace(".", "/")).resolve(builderClassName + ".java"));
        generatedClass.setType(GeneratedClass.ClassType.TEST_DATA);
        generatedClass.setSourceComponent(entityName);
        
        logger.info("Generated test data builder: {}.{}", testPackageName, builderClassName);
        return generatedClass;
    }
    
    private void addWithDefaultValuesMethod(TypeSpec.Builder builderClass, String entityName) {
        MethodSpec.Builder method = MethodSpec.methodBuilder("withDefaultValues")
            .addModifiers(Modifier.PRIVATE)
            .returns(void.class);
        
        // Add common default values based on entity name
        if (entityName.equalsIgnoreCase("Post")) {
            method.addStatement("// entity.setTitle($S)", "Test Post Title")
                  .addStatement("// entity.setContent($S)", "Test post content")
                  .addStatement("// entity.setCreatedDate($T.now())", ClassName.get("java.time", "LocalDateTime"));
        } else if (entityName.equalsIgnoreCase("User")) {
            method.addStatement("// entity.setUsername($S)", "testuser")
                  .addStatement("// entity.setEmail($S)", "<EMAIL>")
                  .addStatement("// entity.setFirstName($S)", "Test")
                  .addStatement("// entity.setLastName($S)", "User");
        } else {
            method.addStatement("// Set default values for $L entity", entityName)
                  .addStatement("// entity.setName($S)", "Test " + entityName);
        }
        
        builderClass.addMethod(method.build());
    }
    
    private void addCommonBuilderMethods(TypeSpec.Builder builderClass, String entityName, 
                                       String builderClassName, String testPackageName) {
        // Add withId method
        builderClass.addMethod(MethodSpec.methodBuilder("withId")
            .addModifiers(Modifier.PUBLIC)
            .addParameter(Long.class, "id")
            .returns(ClassName.get(testPackageName, builderClassName))
            .addStatement("// entity.setId(id)")
            .addStatement("return this")
            .build());
        
        // Add entity-specific methods based on name
        if (entityName.equalsIgnoreCase("Post")) {
            builderClass.addMethod(MethodSpec.methodBuilder("withTitle")
                .addModifiers(Modifier.PUBLIC)
                .addParameter(String.class, "title")
                .returns(ClassName.get(testPackageName, builderClassName))
                .addStatement("// entity.setTitle(title)")
                .addStatement("return this")
                .build());
            
            builderClass.addMethod(MethodSpec.methodBuilder("withContent")
                .addModifiers(Modifier.PUBLIC)
                .addParameter(String.class, "content")
                .returns(ClassName.get(testPackageName, builderClassName))
                .addStatement("// entity.setContent(content)")
                .addStatement("return this")
                .build());
        } else if (entityName.equalsIgnoreCase("User")) {
            builderClass.addMethod(MethodSpec.methodBuilder("withUsername")
                .addModifiers(Modifier.PUBLIC)
                .addParameter(String.class, "username")
                .returns(ClassName.get(testPackageName, builderClassName))
                .addStatement("// entity.setUsername(username)")
                .addStatement("return this")
                .build());
            
            builderClass.addMethod(MethodSpec.methodBuilder("withEmail")
                .addModifiers(Modifier.PUBLIC)
                .addParameter(String.class, "email")
                .returns(ClassName.get(testPackageName, builderClassName))
                .addStatement("// entity.setEmail(email)")
                .addStatement("return this")
                .build());
        }
    }
    
    private GeneratedClass generateTestDataFixture(GeneratedClass entity, Path targetPath) throws IOException {
        String entityName = entity.getClassName();
        String fixtureClassName = entityName + "TestFixture";
        String testPackageName = config.getBasePackage() + ".testdata";
        
        TypeSpec.Builder fixtureClass = TypeSpec.classBuilder(fixtureClassName)
            .addModifiers(Modifier.PUBLIC, Modifier.FINAL)
            .addJavadoc("Test fixtures for $L entity", entityName);
        
        // Add private constructor
        fixtureClass.addMethod(MethodSpec.constructorBuilder()
            .addModifiers(Modifier.PRIVATE)
            .addStatement("// Utility class")
            .build());
        
        // Add fixture methods
        addFixtureMethods(fixtureClass, entityName, testPackageName);
        
        JavaFile javaFile = JavaFile.builder(testPackageName, fixtureClass.build())
            .addFileComment("Generated test fixtures for " + entityName)
            .build();
        
        Path javaPath = targetPath.resolve("src/test/java");
        javaFile.writeTo(javaPath);
        
        GeneratedClass generatedClass = new GeneratedClass();
        generatedClass.setClassName(fixtureClassName);
        generatedClass.setPackageName(testPackageName);
        generatedClass.setFilePath(javaPath.resolve(testPackageName.replace(".", "/")).resolve(fixtureClassName + ".java"));
        generatedClass.setType(GeneratedClass.ClassType.TEST_DATA);
        generatedClass.setSourceComponent(entityName);
        
        logger.info("Generated test fixture: {}.{}", testPackageName, fixtureClassName);
        return generatedClass;
    }
    
    private void addFixtureMethods(TypeSpec.Builder fixtureClass, String entityName, String testPackageName) {
        String builderClassName = entityName + "TestDataBuilder";
        
        // Add default fixture method
        fixtureClass.addMethod(MethodSpec.methodBuilder("default" + entityName)
            .addModifiers(Modifier.PUBLIC, Modifier.STATIC)
            .returns(ClassName.get(config.getBasePackage() + ".entity", entityName))
            .addStatement("return $T.a$L().build()", 
                ClassName.get(testPackageName, builderClassName), entityName)
            .build());
        
        // Add valid fixture method
        fixtureClass.addMethod(MethodSpec.methodBuilder("valid" + entityName)
            .addModifiers(Modifier.PUBLIC, Modifier.STATIC)
            .returns(ClassName.get(config.getBasePackage() + ".entity", entityName))
            .addStatement("return $T.a$L().build()", 
                ClassName.get(testPackageName, builderClassName), entityName)
            .build());
        
        // Add list fixture method
        fixtureClass.addMethod(MethodSpec.methodBuilder("multiple" + entityName + "s")
            .addModifiers(Modifier.PUBLIC, Modifier.STATIC)
            .addParameter(int.class, "count")
            .returns(ParameterizedTypeName.get(ClassName.get(List.class), 
                ClassName.get(config.getBasePackage() + ".entity", entityName)))
            .addStatement("$T<$T> entities = new $T<>()", 
                ClassName.get(List.class),
                ClassName.get(config.getBasePackage() + ".entity", entityName),
                ClassName.get(ArrayList.class))
            .addStatement("for (int i = 0; i < count; i++) {")
            .addStatement("    entities.add($T.a$L().withId((long) (i + 1)).build())", 
                ClassName.get(testPackageName, builderClassName), entityName)
            .addStatement("}")
            .addStatement("return entities")
            .build());
    }
    
    private GeneratedClass generateTestDataFactory(List<GeneratedClass> entities, Path targetPath) throws IOException {
        String factoryClassName = "TestDataFactory";
        String testPackageName = config.getBasePackage() + ".testdata";
        
        TypeSpec.Builder factoryClass = TypeSpec.classBuilder(factoryClassName)
            .addModifiers(Modifier.PUBLIC, Modifier.FINAL)
            .addJavadoc("Central factory for creating test data");
        
        // Add private constructor
        factoryClass.addMethod(MethodSpec.constructorBuilder()
            .addModifiers(Modifier.PRIVATE)
            .addStatement("// Utility class")
            .build());
        
        // Add factory methods for each entity
        for (GeneratedClass entity : entities) {
            String entityName = entity.getClassName();
            String fixtureClassName = entityName + "TestFixture";
            
            factoryClass.addMethod(MethodSpec.methodBuilder("create" + entityName)
                .addModifiers(Modifier.PUBLIC, Modifier.STATIC)
                .returns(ClassName.get(config.getBasePackage() + ".entity", entityName))
                .addStatement("return $T.default$L()", 
                    ClassName.get(testPackageName, fixtureClassName), entityName)
                .build());
        }
        
        JavaFile javaFile = JavaFile.builder(testPackageName, factoryClass.build())
            .addFileComment("Generated test data factory")
            .build();
        
        Path javaPath = targetPath.resolve("src/test/java");
        javaFile.writeTo(javaPath);
        
        GeneratedClass generatedClass = new GeneratedClass();
        generatedClass.setClassName(factoryClassName);
        generatedClass.setPackageName(testPackageName);
        generatedClass.setFilePath(javaPath.resolve(testPackageName.replace(".", "/")).resolve(factoryClassName + ".java"));
        generatedClass.setType(GeneratedClass.ClassType.TEST_DATA);
        
        logger.info("Generated test data factory: {}.{}", testPackageName, factoryClassName);
        return generatedClass;
    }
}
