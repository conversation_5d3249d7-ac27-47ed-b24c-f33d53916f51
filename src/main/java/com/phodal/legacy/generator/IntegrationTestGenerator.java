package com.phodal.legacy.generator;

import com.squareup.javapoet.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.lang.model.element.Modifier;
import java.io.IOException;
import java.nio.file.Path;
import java.util.*;

/**
 * Generates integration tests for complete API workflows using TestRestTemplate.
 */
public class IntegrationTestGenerator {
    
    private static final Logger logger = LoggerFactory.getLogger(IntegrationTestGenerator.class);
    
    private final GenerationConfig config;
    
    public IntegrationTestGenerator(GenerationConfig config) {
        this.config = config;
    }
    
    /**
     * Generate integration tests for all controllers
     */
    public List<GeneratedClass> generateIntegrationTests(List<GeneratedClass> controllers,
                                                        List<GeneratedClass> entities,
                                                        Path targetPath) throws IOException {
        
        List<GeneratedClass> testClasses = new ArrayList<>();
        
        // Generate API integration test
        GeneratedClass apiIntegrationTest = generateApiIntegrationTest(controllers, entities, targetPath);
        testClasses.add(apiIntegrationTest);
        
        // Generate individual controller integration tests
        for (GeneratedClass controller : controllers) {
            Optional<GeneratedClass> entityOpt = findCorrespondingEntity(controller, entities);
            if (entityOpt.isPresent()) {
                GeneratedClass integrationTest = generateControllerIntegrationTest(
                    controller, entityOpt.get(), targetPath);
                testClasses.add(integrationTest);
            }
        }
        
        logger.info("Generated {} integration test classes", testClasses.size());
        return testClasses;
    }
    
    private GeneratedClass generateApiIntegrationTest(List<GeneratedClass> controllers,
                                                    List<GeneratedClass> entities,
                                                    Path targetPath) throws IOException {
        
        String testClassName = "ApiIntegrationTest";
        String testPackageName = config.getBasePackage() + ".integration";
        
        TypeSpec.Builder testClass = TypeSpec.classBuilder(testClassName)
            .addModifiers(Modifier.PUBLIC)
            .addAnnotation(ClassName.get("org.springframework.boot.test.context", "SpringBootTest"))
            .addAnnotation(AnnotationSpec.builder(ClassName.get("org.springframework.boot.test.context", "SpringBootTest"))
                .addMember("webEnvironment", "$T.RANDOM_PORT", 
                    ClassName.get("org.springframework.boot.test.context.SpringBootTest", "WebEnvironment"))
                .build())
            .addAnnotation(ClassName.get("org.springframework.test.context", "ActiveProfiles"))
            .addAnnotation(AnnotationSpec.builder(ClassName.get("org.springframework.test.context", "ActiveProfiles"))
                .addMember("value", "$S", "test")
                .build())
            .addJavadoc("Integration tests for complete API workflows");
        
        // Add TestRestTemplate field
        testClass.addField(FieldSpec.builder(
            ClassName.get("org.springframework.boot.test.web.client", "TestRestTemplate"),
            "restTemplate",
            Modifier.PRIVATE)
            .addAnnotation(ClassName.get("org.springframework.beans.factory.annotation", "Autowired"))
            .build());
        
        // Add port field
        testClass.addField(FieldSpec.builder(
            int.class,
            "port",
            Modifier.PRIVATE)
            .addAnnotation(ClassName.get("org.springframework.boot.test.web.server", "LocalServerPort"))
            .build());
        
        // Add ObjectMapper field
        testClass.addField(FieldSpec.builder(
            ClassName.get("com.fasterxml.jackson.databind", "ObjectMapper"),
            "objectMapper",
            Modifier.PRIVATE)
            .addAnnotation(ClassName.get("org.springframework.beans.factory.annotation", "Autowired"))
            .build());
        
        // Add setup method
        addSetupMethod(testClass);
        
        // Add helper methods
        addHelperMethods(testClass);
        
        // Generate workflow tests for each entity
        for (GeneratedClass entity : entities) {
            addEntityWorkflowTest(testClass, entity);
        }
        
        // Add health check test
        addHealthCheckTest(testClass);
        
        JavaFile javaFile = JavaFile.builder(testPackageName, testClass.build())
            .addFileComment("Generated integration tests for API workflows")
            .build();
        
        Path javaPath = targetPath.resolve("src/test/java");
        javaFile.writeTo(javaPath);
        
        GeneratedClass generatedClass = new GeneratedClass();
        generatedClass.setClassName(testClassName);
        generatedClass.setPackageName(testPackageName);
        generatedClass.setFilePath(javaPath.resolve(testPackageName.replace(".", "/")).resolve(testClassName + ".java"));
        generatedClass.setType(GeneratedClass.ClassType.INTEGRATION_TEST);
        
        logger.info("Generated API integration test: {}.{}", testPackageName, testClassName);
        return generatedClass;
    }
    
    private void addSetupMethod(TypeSpec.Builder testClass) {
        testClass.addMethod(MethodSpec.methodBuilder("setUp")
            .addModifiers(Modifier.PUBLIC)
            .addAnnotation(ClassName.get("org.junit.jupiter.api", "BeforeEach"))
            .addStatement("// Setup test data if needed")
            .build());
    }
    
    private void addHelperMethods(TypeSpec.Builder testClass) {
        // Add URL builder method
        testClass.addMethod(MethodSpec.methodBuilder("createUrl")
            .addModifiers(Modifier.PRIVATE)
            .addParameter(String.class, "path")
            .returns(String.class)
            .addStatement("return $S + port + $S + path", "http://localhost:", config.getApiPrefix())
            .build());
        
        // Add HTTP headers method
        testClass.addMethod(MethodSpec.methodBuilder("createHeaders")
            .addModifiers(Modifier.PRIVATE)
            .returns(ClassName.get("org.springframework.http", "HttpHeaders"))
            .addStatement("$T headers = new $T()", 
                ClassName.get("org.springframework.http", "HttpHeaders"),
                ClassName.get("org.springframework.http", "HttpHeaders"))
            .addStatement("headers.setContentType($T.APPLICATION_JSON)", 
                ClassName.get("org.springframework.http", "MediaType"))
            .addStatement("return headers")
            .build());
    }
    
    private void addEntityWorkflowTest(TypeSpec.Builder testClass, GeneratedClass entity) {
        String entityName = entity.getClassName();
        String entityVarName = entityName.toLowerCase();
        String entityPath = "/" + entityVarName + "s";
        
        MethodSpec.Builder testMethod = MethodSpec.methodBuilder("test" + entityName + "CrudWorkflow")
            .addModifiers(Modifier.PUBLIC)
            .addAnnotation(ClassName.get("org.junit.jupiter.api", "Test"))
            .addAnnotation(ClassName.get("org.junit.jupiter.api", "Order"))
            .addAnnotation(AnnotationSpec.builder(ClassName.get("org.junit.jupiter.api", "Order"))
                .addMember("value", "1")
                .build())
            .addStatement("// Test complete CRUD workflow for $L", entityName)
            .addStatement("")
            .addStatement("// 1. Create a new $L", entityVarName)
            .addStatement("$T new$L = new $T()", 
                ClassName.get(config.getBasePackage() + ".entity", entityName),
                entityName,
                ClassName.get(config.getBasePackage() + ".entity", entityName))
            .addStatement("$T<$T> createRequest = new $T<>(new$L, createHeaders())", 
                ClassName.get("org.springframework.http", "HttpEntity"),
                ClassName.get(config.getBasePackage() + ".entity", entityName),
                ClassName.get("org.springframework.http", "HttpEntity"),
                entityName)
            .addStatement("")
            .addStatement("$T<$T> createResponse = restTemplate.postForEntity(", 
                ClassName.get("org.springframework.http", "ResponseEntity"),
                ClassName.get(config.getBasePackage() + ".entity", entityName))
            .addStatement("    createUrl($S), createRequest, $T.class)", 
                entityPath, ClassName.get(config.getBasePackage() + ".entity", entityName))
            .addStatement("")
            .addStatement("$T.assertEquals($T.CREATED, createResponse.getStatusCode())", 
                ClassName.get("org.junit.jupiter.api", "Assertions"),
                ClassName.get("org.springframework.http", "HttpStatus"))
            .addStatement("$T.assertNotNull(createResponse.getBody())", 
                ClassName.get("org.junit.jupiter.api", "Assertions"))
            .addStatement("")
            .addStatement("// 2. Get all $Ls", entityVarName)
            .addStatement("$T<$T[]> getAllResponse = restTemplate.getForEntity(", 
                ClassName.get("org.springframework.http", "ResponseEntity"),
                ClassName.get(config.getBasePackage() + ".entity", entityName))
            .addStatement("    createUrl($S), $T[].class)", 
                entityPath, ClassName.get(config.getBasePackage() + ".entity", entityName))
            .addStatement("")
            .addStatement("$T.assertEquals($T.OK, getAllResponse.getStatusCode())", 
                ClassName.get("org.junit.jupiter.api", "Assertions"),
                ClassName.get("org.springframework.http", "HttpStatus"))
            .addStatement("$T.assertNotNull(getAllResponse.getBody())", 
                ClassName.get("org.junit.jupiter.api", "Assertions"))
            .addStatement("$T.assertTrue(getAllResponse.getBody().length > 0)", 
                ClassName.get("org.junit.jupiter.api", "Assertions"));
        
        testClass.addMethod(testMethod.build());
    }
    
    private void addHealthCheckTest(TypeSpec.Builder testClass) {
        testClass.addMethod(MethodSpec.methodBuilder("testHealthCheck")
            .addModifiers(Modifier.PUBLIC)
            .addAnnotation(ClassName.get("org.junit.jupiter.api", "Test"))
            .addStatement("$T<String> response = restTemplate.getForEntity(", 
                ClassName.get("org.springframework.http", "ResponseEntity"))
            .addStatement("    $S + port + $S, String.class)", "http://localhost:", "/actuator/health")
            .addStatement("")
            .addStatement("$T.assertEquals($T.OK, response.getStatusCode())", 
                ClassName.get("org.junit.jupiter.api", "Assertions"),
                ClassName.get("org.springframework.http", "HttpStatus"))
            .build());
    }
    
    private GeneratedClass generateControllerIntegrationTest(GeneratedClass controller,
                                                           GeneratedClass entity,
                                                           Path targetPath) throws IOException {
        
        String testClassName = controller.getClassName() + "IntegrationTest";
        String testPackageName = config.getBasePackage() + ".integration";
        
        // Similar structure to API integration test but focused on specific controller
        TypeSpec.Builder testClass = TypeSpec.classBuilder(testClassName)
            .addModifiers(Modifier.PUBLIC)
            .addAnnotation(ClassName.get("org.springframework.boot.test.context", "SpringBootTest"))
            .addAnnotation(AnnotationSpec.builder(ClassName.get("org.springframework.boot.test.context", "SpringBootTest"))
                .addMember("webEnvironment", "$T.RANDOM_PORT", 
                    ClassName.get("org.springframework.boot.test.context.SpringBootTest", "WebEnvironment"))
                .build())
            .addJavadoc("Integration tests for $L", controller.getClassName());
        
        // Add basic test structure (simplified for brevity)
        testClass.addField(FieldSpec.builder(
            ClassName.get("org.springframework.boot.test.web.client", "TestRestTemplate"),
            "restTemplate",
            Modifier.PRIVATE)
            .addAnnotation(ClassName.get("org.springframework.beans.factory.annotation", "Autowired"))
            .build());
        
        JavaFile javaFile = JavaFile.builder(testPackageName, testClass.build())
            .addFileComment("Generated integration test for " + controller.getClassName())
            .build();
        
        Path javaPath = targetPath.resolve("src/test/java");
        javaFile.writeTo(javaPath);
        
        GeneratedClass generatedClass = new GeneratedClass();
        generatedClass.setClassName(testClassName);
        generatedClass.setPackageName(testPackageName);
        generatedClass.setFilePath(javaPath.resolve(testPackageName.replace(".", "/")).resolve(testClassName + ".java"));
        generatedClass.setType(GeneratedClass.ClassType.INTEGRATION_TEST);
        generatedClass.setSourceComponent(controller.getClassName());
        
        return generatedClass;
    }
    
    private Optional<GeneratedClass> findCorrespondingEntity(GeneratedClass controller, List<GeneratedClass> entities) {
        String controllerName = controller.getClassName().replace("Controller", "");
        return entities.stream()
            .filter(e -> e.getClassName().toLowerCase().contains(controllerName.toLowerCase()))
            .findFirst();
    }
}
