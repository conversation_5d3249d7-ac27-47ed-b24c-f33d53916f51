package com.phodal.legacy.generator;

import java.nio.file.Path;
import java.util.ArrayList;
import java.util.List;

/**
 * Result of the JSP to Spring Boot conversion process.
 */
public class ConversionResult {
    
    private boolean success;
    private String errorMessage;
    private Path targetPath;
    private List<GeneratedClass> generatedClasses = new ArrayList<>();
    private List<String> warnings = new ArrayList<>();
    private long duration;
    
    public ConversionResult() {
    }
    
    public boolean isSuccess() {
        return success;
    }
    
    public void setSuccess(boolean success) {
        this.success = success;
    }
    
    public String getErrorMessage() {
        return errorMessage;
    }
    
    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }
    
    public Path getTargetPath() {
        return targetPath;
    }
    
    public void setTargetPath(Path targetPath) {
        this.targetPath = targetPath;
    }
    
    public List<GeneratedClass> getGeneratedClasses() {
        return generatedClasses;
    }
    
    public void setGeneratedClasses(List<GeneratedClass> generatedClasses) {
        this.generatedClasses = generatedClasses;
    }
    
    public void addGeneratedClass(GeneratedClass generatedClass) {
        this.generatedClasses.add(generatedClass);
    }
    
    public void addGeneratedClasses(List<GeneratedClass> generatedClasses) {
        this.generatedClasses.addAll(generatedClasses);
    }
    
    public List<String> getWarnings() {
        return warnings;
    }
    
    public void setWarnings(List<String> warnings) {
        this.warnings = warnings;
    }
    
    public void addWarning(String warning) {
        this.warnings.add(warning);
    }
    
    public long getDuration() {
        return duration;
    }
    
    public void setDuration(long duration) {
        this.duration = duration;
    }
    
    public int getGeneratedClassCount() {
        return generatedClasses.size();
    }
    
    public int getControllerCount() {
        return (int) generatedClasses.stream()
            .filter(gc -> gc.getType() == GeneratedClass.ClassType.CONTROLLER)
            .count();
    }
    
    public int getServiceCount() {
        return (int) generatedClasses.stream()
            .filter(gc -> gc.getType() == GeneratedClass.ClassType.SERVICE)
            .count();
    }
    
    public int getEntityCount() {
        return (int) generatedClasses.stream()
            .filter(gc -> gc.getType() == GeneratedClass.ClassType.ENTITY)
            .count();
    }
    
    public int getRepositoryCount() {
        return (int) generatedClasses.stream()
            .filter(gc -> gc.getType() == GeneratedClass.ClassType.REPOSITORY)
            .count();
    }
    
    @Override
    public String toString() {
        return "ConversionResult{" +
                "success=" + success +
                ", targetPath=" + targetPath +
                ", generatedClasses=" + generatedClasses.size() +
                ", warnings=" + warnings.size() +
                ", duration=" + duration +
                '}';
    }
}
