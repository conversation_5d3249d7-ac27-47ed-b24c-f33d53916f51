package com.phodal.legacy.generator;

import com.phodal.legacy.model.ComponentModel;
import com.squareup.javapoet.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.lang.model.element.Modifier;
import java.io.IOException;
import java.nio.file.Path;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Generates Spring Boot REST controllers from JSP pages and servlets.
 */
public class RestControllerGenerator {
    
    private static final Logger logger = LoggerFactory.getLogger(RestControllerGenerator.class);
    
    private final GenerationConfig config;
    
    public RestControllerGenerator(GenerationConfig config) {
        this.config = config;
    }
    
    /**
     * Generate REST controllers from JSP components and servlets
     */
    public List<GeneratedClass> generateControllers(List<ComponentModel> jspComponents,
                                                   List<ComponentModel> servletComponents,
                                                   List<GeneratedClass> services,
                                                   Path targetPath) throws IOException {
        
        List<GeneratedClass> controllers = new ArrayList<>();
        
        // Group JSP components by logical functionality
        Map<String, List<ComponentModel>> groupedJsps = groupJspsByFunction(jspComponents);
        
        for (Map.Entry<String, List<ComponentModel>> entry : groupedJsps.entrySet()) {
            String functionName = entry.getKey();
            List<ComponentModel> jsps = entry.getValue();
            
            GeneratedClass controller = generateControllerForFunction(functionName, jsps, services, targetPath);
            controllers.add(controller);
        }
        
        // Generate controllers for servlets that don't have corresponding JSPs
        for (ComponentModel servlet : servletComponents) {
            if (!isServletCoveredByJsp(servlet, jspComponents)) {
                GeneratedClass controller = generateControllerForServlet(servlet, services, targetPath);
                controllers.add(controller);
            }
        }
        
        logger.info("Generated {} REST controllers", controllers.size());
        return controllers;
    }
    
    private Map<String, List<ComponentModel>> groupJspsByFunction(List<ComponentModel> jspComponents) {
        Map<String, List<ComponentModel>> groups = new HashMap<>();
        
        for (ComponentModel jsp : jspComponents) {
            String functionName = extractFunctionName(jsp.getName());
            groups.computeIfAbsent(functionName, k -> new ArrayList<>()).add(jsp);
        }
        
        return groups;
    }
    
    private String extractFunctionName(String jspName) {
        // Extract function name from JSP file name
        // e.g., "posts.jsp" -> "Post", "create.jsp" -> "Post", "edit.jsp" -> "Post"
        String baseName = jspName.replace(".jsp", "");
        
        // Common patterns
        if (baseName.equals("index") || baseName.equals("list") || baseName.equals("posts")) {
            return "Post";
        } else if (baseName.equals("create") || baseName.equals("add") || baseName.equals("new")) {
            return "Post";
        } else if (baseName.equals("edit") || baseName.equals("update")) {
            return "Post";
        } else if (baseName.equals("delete") || baseName.equals("remove")) {
            return "Post";
        } else if (baseName.equals("login") || baseName.equals("auth")) {
            return "Auth";
        } else if (baseName.equals("error")) {
            return "Error";
        }
        
        // Default: capitalize first letter
        return Character.toUpperCase(baseName.charAt(0)) + baseName.substring(1);
    }
    
    private GeneratedClass generateControllerForFunction(String functionName, 
                                                       List<ComponentModel> jsps,
                                                       List<GeneratedClass> services,
                                                       Path targetPath) throws IOException {
        
        String className = functionName + "Controller";
        String packageName = config.getBasePackage() + ".controller";
        
        // Find corresponding service
        Optional<GeneratedClass> serviceOpt = services.stream()
            .filter(s -> s.getClassName().toLowerCase().contains(functionName.toLowerCase()))
            .findFirst();
        
        TypeSpec.Builder controllerClass = TypeSpec.classBuilder(className)
            .addModifiers(Modifier.PUBLIC)
            .addAnnotation(ClassName.get("org.springframework.web.bind.annotation", "RestController"))
            .addAnnotation(AnnotationSpec.builder(ClassName.get("org.springframework.web.bind.annotation", "RequestMapping"))
                .addMember("value", "$S", config.getApiPrefix() + "/" + functionName.toLowerCase() + "s")
                .build());
        
        if (config.isEnableCors()) {
            controllerClass.addAnnotation(ClassName.get("org.springframework.web.bind.annotation", "CrossOrigin"));
        }
        
        // Add service dependency if available
        if (serviceOpt.isPresent()) {
            GeneratedClass service = serviceOpt.get();
            String serviceFieldName = Character.toLowerCase(service.getClassName().charAt(0)) + 
                                    service.getClassName().substring(1);
            
            controllerClass.addField(FieldSpec.builder(
                ClassName.get(service.getPackageName(), service.getClassName()),
                serviceFieldName,
                Modifier.PRIVATE, Modifier.FINAL)
                .build());
            
            // Constructor
            controllerClass.addMethod(MethodSpec.constructorBuilder()
                .addModifiers(Modifier.PUBLIC)
                .addParameter(ClassName.get(service.getPackageName(), service.getClassName()), serviceFieldName)
                .addStatement("this.$N = $N", serviceFieldName, serviceFieldName)
                .build());
        }
        
        // Generate REST endpoints based on JSP pages
        for (ComponentModel jsp : jsps) {
            addRestEndpointForJsp(controllerClass, jsp, functionName, serviceOpt.orElse(null));
        }
        
        // Add common CRUD endpoints if not already present
        addStandardCrudEndpoints(controllerClass, functionName, serviceOpt.orElse(null));
        
        JavaFile javaFile = JavaFile.builder(packageName, controllerClass.build())
            .addFileComment("Generated REST controller from JSP pages: " + 
                jsps.stream().map(ComponentModel::getName).collect(Collectors.joining(", ")))
            .build();
        
        Path javaPath = targetPath.resolve("src/main/java");
        javaFile.writeTo(javaPath);
        
        GeneratedClass generatedClass = new GeneratedClass();
        generatedClass.setClassName(className);
        generatedClass.setPackageName(packageName);
        generatedClass.setFilePath(javaPath.resolve(packageName.replace(".", "/")).resolve(className + ".java"));
        generatedClass.setType(GeneratedClass.ClassType.CONTROLLER);
        generatedClass.setSourceComponent(jsps.stream().map(ComponentModel::getName).collect(Collectors.joining(", ")));
        
        logger.info("Generated REST controller: {}.{}", packageName, className);
        return generatedClass;
    }
    
    private void addRestEndpointForJsp(TypeSpec.Builder controllerClass, ComponentModel jsp, 
                                     String functionName, GeneratedClass service) {
        String jspName = jsp.getName().replace(".jsp", "");
        
        if (jspName.equals("posts") || jspName.equals("index") || jspName.equals("list")) {
            // GET /posts - list all
            addGetAllEndpoint(controllerClass, functionName, service);
        } else if (jspName.equals("post")) {
            // GET /posts/{id} - get single
            addGetByIdEndpoint(controllerClass, functionName, service);
        } else if (jspName.equals("create") || jspName.equals("add")) {
            // POST /posts - create new
            addCreateEndpoint(controllerClass, functionName, service);
        } else if (jspName.equals("edit") || jspName.equals("update")) {
            // PUT /posts/{id} - update
            addUpdateEndpoint(controllerClass, functionName, service);
        }
    }
    
    private void addGetAllEndpoint(TypeSpec.Builder controllerClass, String functionName, GeneratedClass service) {
        String entityName = functionName;
        String methodName = "getAll" + entityName + "s";
        
        MethodSpec.Builder method = MethodSpec.methodBuilder(methodName)
            .addModifiers(Modifier.PUBLIC)
            .addAnnotation(ClassName.get("org.springframework.web.bind.annotation", "GetMapping"))
            .returns(ParameterizedTypeName.get(
                ClassName.get("org.springframework.http", "ResponseEntity"),
                ParameterizedTypeName.get(ClassName.get(List.class), 
                    ClassName.get(config.getBasePackage() + ".entity", entityName))));
        
        if (service != null) {
            String serviceField = Character.toLowerCase(service.getClassName().charAt(0)) + 
                                service.getClassName().substring(1);
            method.addStatement("$T $N = $N.findAll()", 
                ParameterizedTypeName.get(ClassName.get(List.class), 
                    ClassName.get(config.getBasePackage() + ".entity", entityName)),
                entityName.toLowerCase() + "s", serviceField);
            method.addStatement("return $T.ok($N)", 
                ClassName.get("org.springframework.http", "ResponseEntity"), 
                entityName.toLowerCase() + "s");
        } else {
            method.addStatement("// TODO: Implement get all $N logic", entityName.toLowerCase() + "s");
            method.addStatement("return $T.ok($T.emptyList())", 
                ClassName.get("org.springframework.http", "ResponseEntity"),
                ClassName.get(Collections.class));
        }
        
        controllerClass.addMethod(method.build());
    }

    private void addGetByIdEndpoint(TypeSpec.Builder controllerClass, String functionName, GeneratedClass service) {
        String entityName = functionName;
        String methodName = "get" + entityName + "ById";

        MethodSpec.Builder method = MethodSpec.methodBuilder(methodName)
            .addModifiers(Modifier.PUBLIC)
            .addAnnotation(AnnotationSpec.builder(ClassName.get("org.springframework.web.bind.annotation", "GetMapping"))
                .addMember("value", "$S", "/{id}")
                .build())
            .addParameter(ParameterSpec.builder(Long.class, "id")
                .addAnnotation(ClassName.get("org.springframework.web.bind.annotation", "PathVariable"))
                .build())
            .returns(ParameterizedTypeName.get(
                ClassName.get("org.springframework.http", "ResponseEntity"),
                ClassName.get(config.getBasePackage() + ".entity", entityName)));

        if (service != null) {
            String serviceField = Character.toLowerCase(service.getClassName().charAt(0)) +
                                service.getClassName().substring(1);
            method.addStatement("$T $N = $N.findById(id)",
                ParameterizedTypeName.get(ClassName.get(Optional.class),
                    ClassName.get(config.getBasePackage() + ".entity", entityName)),
                entityName.toLowerCase(), serviceField);
            method.addStatement("return $N.map($T::ok).orElse($T.notFound().build())",
                entityName.toLowerCase(),
                ClassName.get("org.springframework.http", "ResponseEntity"),
                ClassName.get("org.springframework.http", "ResponseEntity"));
        } else {
            method.addStatement("// TODO: Implement get $N by id logic", entityName.toLowerCase());
            method.addStatement("return $T.notFound().build()",
                ClassName.get("org.springframework.http", "ResponseEntity"));
        }

        controllerClass.addMethod(method.build());
    }

    private void addCreateEndpoint(TypeSpec.Builder controllerClass, String functionName, GeneratedClass service) {
        String entityName = functionName;
        String methodName = "create" + entityName;

        MethodSpec.Builder method = MethodSpec.methodBuilder(methodName)
            .addModifiers(Modifier.PUBLIC)
            .addAnnotation(ClassName.get("org.springframework.web.bind.annotation", "PostMapping"))
            .addParameter(ParameterSpec.builder(ClassName.get(config.getBasePackage() + ".entity", entityName), entityName.toLowerCase())
                .addAnnotation(ClassName.get("org.springframework.web.bind.annotation", "RequestBody"))
                .build())
            .returns(ParameterizedTypeName.get(
                ClassName.get("org.springframework.http", "ResponseEntity"),
                ClassName.get(config.getBasePackage() + ".entity", entityName)));

        if (service != null) {
            String serviceField = Character.toLowerCase(service.getClassName().charAt(0)) +
                                service.getClassName().substring(1);
            method.addStatement("$T saved$N = $N.save($N)",
                ClassName.get(config.getBasePackage() + ".entity", entityName),
                entityName, serviceField, entityName.toLowerCase());
            method.addStatement("return $T.status($T.CREATED).body(saved$N)",
                ClassName.get("org.springframework.http", "ResponseEntity"),
                ClassName.get("org.springframework.http", "HttpStatus"),
                entityName);
        } else {
            method.addStatement("// TODO: Implement create $N logic", entityName.toLowerCase());
            method.addStatement("return $T.status($T.CREATED).body($N)",
                ClassName.get("org.springframework.http", "ResponseEntity"),
                ClassName.get("org.springframework.http", "HttpStatus"),
                entityName.toLowerCase());
        }

        controllerClass.addMethod(method.build());
    }

    private void addUpdateEndpoint(TypeSpec.Builder controllerClass, String functionName, GeneratedClass service) {
        String entityName = functionName;
        String methodName = "update" + entityName;

        MethodSpec.Builder method = MethodSpec.methodBuilder(methodName)
            .addModifiers(Modifier.PUBLIC)
            .addAnnotation(AnnotationSpec.builder(ClassName.get("org.springframework.web.bind.annotation", "PutMapping"))
                .addMember("value", "$S", "/{id}")
                .build())
            .addParameter(ParameterSpec.builder(Long.class, "id")
                .addAnnotation(ClassName.get("org.springframework.web.bind.annotation", "PathVariable"))
                .build())
            .addParameter(ParameterSpec.builder(ClassName.get(config.getBasePackage() + ".entity", entityName), entityName.toLowerCase())
                .addAnnotation(ClassName.get("org.springframework.web.bind.annotation", "RequestBody"))
                .build())
            .returns(ParameterizedTypeName.get(
                ClassName.get("org.springframework.http", "ResponseEntity"),
                ClassName.get(config.getBasePackage() + ".entity", entityName)));

        if (service != null) {
            String serviceField = Character.toLowerCase(service.getClassName().charAt(0)) +
                                service.getClassName().substring(1);
            method.addStatement("$T updated$N = $N.update(id, $N)",
                ClassName.get(config.getBasePackage() + ".entity", entityName),
                entityName, serviceField, entityName.toLowerCase());
            method.addStatement("return $T.ok(updated$N)",
                ClassName.get("org.springframework.http", "ResponseEntity"),
                entityName);
        } else {
            method.addStatement("// TODO: Implement update $N logic", entityName.toLowerCase());
            method.addStatement("return $T.ok($N)",
                ClassName.get("org.springframework.http", "ResponseEntity"),
                entityName.toLowerCase());
        }

        controllerClass.addMethod(method.build());
    }

    private void addStandardCrudEndpoints(TypeSpec.Builder controllerClass, String functionName, GeneratedClass service) {
        // Add DELETE endpoint
        String entityName = functionName;
        String methodName = "delete" + entityName;

        MethodSpec.Builder deleteMethod = MethodSpec.methodBuilder(methodName)
            .addModifiers(Modifier.PUBLIC)
            .addAnnotation(AnnotationSpec.builder(ClassName.get("org.springframework.web.bind.annotation", "DeleteMapping"))
                .addMember("value", "$S", "/{id}")
                .build())
            .addParameter(ParameterSpec.builder(Long.class, "id")
                .addAnnotation(ClassName.get("org.springframework.web.bind.annotation", "PathVariable"))
                .build())
            .returns(ParameterizedTypeName.get(
                ClassName.get("org.springframework.http", "ResponseEntity"),
                ClassName.get(Void.class)));

        if (service != null) {
            String serviceField = Character.toLowerCase(service.getClassName().charAt(0)) +
                                service.getClassName().substring(1);
            deleteMethod.addStatement("$N.deleteById(id)", serviceField);
            deleteMethod.addStatement("return $T.noContent().build()",
                ClassName.get("org.springframework.http", "ResponseEntity"));
        } else {
            deleteMethod.addStatement("// TODO: Implement delete $N logic", entityName.toLowerCase());
            deleteMethod.addStatement("return $T.noContent().build()",
                ClassName.get("org.springframework.http", "ResponseEntity"));
        }

        controllerClass.addMethod(deleteMethod.build());
    }

    private GeneratedClass generateControllerForServlet(ComponentModel servlet,
                                                      List<GeneratedClass> services,
                                                      Path targetPath) throws IOException {
        String servletName = servlet.getName();

        // Remove .java extension if present
        if (servletName.endsWith(".java")) {
            servletName = servletName.substring(0, servletName.length() - 5);
        }

        String className = servletName + "Controller";
        String packageName = config.getBasePackage() + ".controller";

        TypeSpec.Builder controllerClass = TypeSpec.classBuilder(className)
            .addModifiers(Modifier.PUBLIC)
            .addAnnotation(ClassName.get("org.springframework.web.bind.annotation", "RestController"))
            .addAnnotation(AnnotationSpec.builder(ClassName.get("org.springframework.web.bind.annotation", "RequestMapping"))
                .addMember("value", "$S", config.getApiPrefix() + "/" + servletName.toLowerCase())
                .build());

        // Add basic endpoint
        MethodSpec.Builder method = MethodSpec.methodBuilder("handle")
            .addModifiers(Modifier.PUBLIC)
            .addAnnotation(ClassName.get("org.springframework.web.bind.annotation", "GetMapping"))
            .returns(ParameterizedTypeName.get(
                ClassName.get("org.springframework.http", "ResponseEntity"),
                ClassName.get(String.class)))
            .addStatement("// TODO: Implement servlet logic for $N", servletName)
            .addStatement("return $T.ok($S)",
                ClassName.get("org.springframework.http", "ResponseEntity"),
                "Servlet " + servletName + " converted to REST endpoint");

        controllerClass.addMethod(method.build());

        JavaFile javaFile = JavaFile.builder(packageName, controllerClass.build())
            .addFileComment("Generated REST controller from servlet: " + servletName)
            .build();

        Path javaPath = targetPath.resolve("src/main/java");
        javaFile.writeTo(javaPath);

        GeneratedClass generatedClass = new GeneratedClass();
        generatedClass.setClassName(className);
        generatedClass.setPackageName(packageName);
        generatedClass.setFilePath(javaPath.resolve(packageName.replace(".", "/")).resolve(className + ".java"));
        generatedClass.setType(GeneratedClass.ClassType.CONTROLLER);
        generatedClass.setSourceComponent(servletName);

        logger.info("Generated REST controller from servlet: {}.{}", packageName, className);
        return generatedClass;
    }

    private boolean isServletCoveredByJsp(ComponentModel servlet, List<ComponentModel> jspComponents) {
        // Simple heuristic: if servlet name matches any JSP name pattern
        String servletName = servlet.getName().toLowerCase();
        return jspComponents.stream()
            .anyMatch(jsp -> jsp.getName().toLowerCase().contains(servletName) ||
                           servletName.contains(jsp.getName().replace(".jsp", "").toLowerCase()));
    }
}
