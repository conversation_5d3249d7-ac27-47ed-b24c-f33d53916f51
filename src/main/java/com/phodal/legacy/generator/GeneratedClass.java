package com.phodal.legacy.generator;

import java.nio.file.Path;
import java.util.ArrayList;
import java.util.List;

/**
 * Represents a generated Java class during the conversion process.
 */
public class GeneratedClass {
    
    public enum ClassType {
        CONTROLLER,
        SERVICE,
        ENTITY,
        REPOSITORY,
        CONFIG,
        MAIN_APPLICATION,
        TEST,
        TEST_CONFIG,
        INTEGRATION_TEST,
        TEST_DATA
    }
    
    private String className;
    private String packageName;
    private Path filePath;
    private ClassType type;
    private List<String> dependencies = new ArrayList<>();
    private String sourceComponent; // Original JSP or Java component that generated this class
    private boolean hasTests;
    
    public GeneratedClass() {
    }
    
    public GeneratedClass(String className, String packageName, ClassType type) {
        this.className = className;
        this.packageName = packageName;
        this.type = type;
    }
    
    public String getClassName() {
        return className;
    }
    
    public void setClassName(String className) {
        this.className = className;
    }
    
    public String getPackageName() {
        return packageName;
    }
    
    public void setPackageName(String packageName) {
        this.packageName = packageName;
    }
    
    public String getFullyQualifiedName() {
        return packageName + "." + className;
    }
    
    public Path getFilePath() {
        return filePath;
    }
    
    public void setFilePath(Path filePath) {
        this.filePath = filePath;
    }
    
    public ClassType getType() {
        return type;
    }
    
    public void setType(ClassType type) {
        this.type = type;
    }
    
    public List<String> getDependencies() {
        return dependencies;
    }
    
    public void setDependencies(List<String> dependencies) {
        this.dependencies = dependencies;
    }
    
    public void addDependency(String dependency) {
        if (!this.dependencies.contains(dependency)) {
            this.dependencies.add(dependency);
        }
    }
    
    public String getSourceComponent() {
        return sourceComponent;
    }
    
    public void setSourceComponent(String sourceComponent) {
        this.sourceComponent = sourceComponent;
    }
    
    public boolean isHasTests() {
        return hasTests;
    }
    
    public void setHasTests(boolean hasTests) {
        this.hasTests = hasTests;
    }
    
    @Override
    public String toString() {
        return "GeneratedClass{" +
                "className='" + className + '\'' +
                ", packageName='" + packageName + '\'' +
                ", type=" + type +
                ", sourceComponent='" + sourceComponent + '\'' +
                '}';
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        
        GeneratedClass that = (GeneratedClass) o;
        
        if (!className.equals(that.className)) return false;
        return packageName.equals(that.packageName);
    }
    
    @Override
    public int hashCode() {
        int result = className.hashCode();
        result = 31 * result + packageName.hashCode();
        return result;
    }
}
