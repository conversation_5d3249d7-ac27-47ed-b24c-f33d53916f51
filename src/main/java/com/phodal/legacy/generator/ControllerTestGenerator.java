package com.phodal.legacy.generator;

import com.squareup.javapoet.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.lang.model.element.Modifier;
import java.io.IOException;
import java.nio.file.Path;
import java.util.*;

/**
 * Generates unit tests for REST controllers using MockMvc and Mockito.
 */
public class ControllerTestGenerator {
    
    private static final Logger logger = LoggerFactory.getLogger(ControllerTestGenerator.class);
    
    private final GenerationConfig config;
    
    public ControllerTestGenerator(GenerationConfig config) {
        this.config = config;
    }
    
    /**
     * Generate unit tests for all controllers
     */
    public List<GeneratedClass> generateControllerTests(List<GeneratedClass> controllers,
                                                       List<GeneratedClass> services,
                                                       List<GeneratedClass> entities,
                                                       Path targetPath) throws IOException {
        
        List<GeneratedClass> testClasses = new ArrayList<>();
        
        for (GeneratedClass controller : controllers) {
            GeneratedClass testClass = generateControllerTest(controller, services, entities, targetPath);
            testClasses.add(testClass);
        }
        
        logger.info("Generated {} controller test classes", testClasses.size());
        return testClasses;
    }
    
    private GeneratedClass generateControllerTest(GeneratedClass controller,
                                                List<GeneratedClass> services,
                                                List<GeneratedClass> entities,
                                                Path targetPath) throws IOException {
        
        String testClassName = controller.getClassName() + "Test";
        String testPackageName = config.getBasePackage() + ".controller";
        
        // Find corresponding service and entity
        Optional<GeneratedClass> serviceOpt = findCorrespondingService(controller, services);
        Optional<GeneratedClass> entityOpt = findCorrespondingEntity(controller, entities);
        
        TypeSpec.Builder testClass = TypeSpec.classBuilder(testClassName)
            .addModifiers(Modifier.PUBLIC)
            .addAnnotation(ClassName.get("org.springframework.boot.test.autoconfigure.web.servlet", "WebMvcTest"))
            .addAnnotation(AnnotationSpec.builder(ClassName.get("org.springframework.boot.test.autoconfigure.web.servlet", "WebMvcTest"))
                .addMember("value", "$T.class", ClassName.get(controller.getPackageName(), controller.getClassName()))
                .build())
            .addJavadoc("Unit tests for $L using MockMvc", controller.getClassName());
        
        // Add MockMvc field
        testClass.addField(FieldSpec.builder(
            ClassName.get("org.springframework.test.web.servlet", "MockMvc"),
            "mockMvc",
            Modifier.PRIVATE)
            .addAnnotation(ClassName.get("org.springframework.beans.factory.annotation", "Autowired"))
            .build());
        
        // Add ObjectMapper field for JSON serialization
        testClass.addField(FieldSpec.builder(
            ClassName.get("com.fasterxml.jackson.databind", "ObjectMapper"),
            "objectMapper",
            Modifier.PRIVATE)
            .addAnnotation(ClassName.get("org.springframework.beans.factory.annotation", "Autowired"))
            .build());
        
        // Add mocked service field if service exists
        if (serviceOpt.isPresent()) {
            GeneratedClass service = serviceOpt.get();
            String serviceFieldName = getServiceFieldName(service.getClassName());
            
            testClass.addField(FieldSpec.builder(
                ClassName.get(service.getPackageName(), service.getClassName()),
                serviceFieldName,
                Modifier.PRIVATE)
                .addAnnotation(ClassName.get("org.springframework.boot.test.mock.mockito", "MockBean"))
                .build());
        }
        
        // Generate test methods
        if (entityOpt.isPresent()) {
            GeneratedClass entity = entityOpt.get();
            addCrudTestMethods(testClass, controller, serviceOpt.orElse(null), entity);
        } else {
            addBasicTestMethods(testClass, controller, serviceOpt.orElse(null));
        }
        
        JavaFile javaFile = JavaFile.builder(testPackageName, testClass.build())
            .addFileComment("Generated unit tests for " + controller.getClassName())
            .build();
        
        Path javaPath = targetPath.resolve("src/test/java");
        javaFile.writeTo(javaPath);
        
        GeneratedClass generatedClass = new GeneratedClass();
        generatedClass.setClassName(testClassName);
        generatedClass.setPackageName(testPackageName);
        generatedClass.setFilePath(javaPath.resolve(testPackageName.replace(".", "/")).resolve(testClassName + ".java"));
        generatedClass.setType(GeneratedClass.ClassType.TEST);
        generatedClass.setSourceComponent(controller.getClassName());
        
        logger.info("Generated controller test: {}.{}", testPackageName, testClassName);
        return generatedClass;
    }
    
    private void addCrudTestMethods(TypeSpec.Builder testClass, GeneratedClass controller,
                                  GeneratedClass service, GeneratedClass entity) {
        String entityName = entity.getClassName();
        String entityVarName = entityName.toLowerCase();
        String serviceFieldName = service != null ? getServiceFieldName(service.getClassName()) : null;
        String controllerPath = extractControllerPath(controller.getClassName());
        
        // Test GET all entities
        addGetAllEntitiesTest(testClass, entityName, entityVarName, serviceFieldName, controllerPath);
        
        // Test GET entity by ID
        addGetEntityByIdTest(testClass, entityName, entityVarName, serviceFieldName, controllerPath);
        
        // Test POST create entity
        addCreateEntityTest(testClass, entityName, entityVarName, serviceFieldName, controllerPath, entity);
        
        // Test PUT update entity
        addUpdateEntityTest(testClass, entityName, entityVarName, serviceFieldName, controllerPath, entity);
        
        // Test DELETE entity
        addDeleteEntityTest(testClass, entityName, entityVarName, serviceFieldName, controllerPath);
    }
    
    private void addGetAllEntitiesTest(TypeSpec.Builder testClass, String entityName, 
                                     String entityVarName, String serviceFieldName, String controllerPath) {
        MethodSpec.Builder testMethod = MethodSpec.methodBuilder("testGetAll" + entityName + "s")
            .addModifiers(Modifier.PUBLIC)
            .addAnnotation(ClassName.get("org.junit.jupiter.api", "Test"))
            .addException(Exception.class)
            .addStatement("// Given")
            .addStatement("$T $L1 = new $T()", 
                ClassName.get(config.getBasePackage() + ".entity", entityName),
                entityVarName,
                ClassName.get(config.getBasePackage() + ".entity", entityName))
            .addStatement("$T $L2 = new $T()", 
                ClassName.get(config.getBasePackage() + ".entity", entityName),
                entityVarName,
                ClassName.get(config.getBasePackage() + ".entity", entityName))
            .addStatement("$T $Ls = $T.asList($L1, $L2)", 
                ParameterizedTypeName.get(ClassName.get(List.class), 
                    ClassName.get(config.getBasePackage() + ".entity", entityName)),
                entityVarName, ClassName.get(Arrays.class), entityVarName, entityVarName);
        
        if (serviceFieldName != null) {
            testMethod.addStatement("$T.when($L.findAll()).thenReturn($Ls)", 
                ClassName.get("org.mockito", "Mockito"), serviceFieldName, entityVarName);
        }
        
        testMethod.addStatement("")
            .addStatement("// When & Then")
            .addStatement("mockMvc.perform($T.get($S))", 
                ClassName.get("org.springframework.test.web.servlet.request", "MockMvcRequestBuilders"),
                controllerPath)
            .addStatement("    .andExpect($T.status().isOk())", 
                ClassName.get("org.springframework.test.web.servlet.result", "MockMvcResultMatchers"))
            .addStatement("    .andExpect($T.content().contentType($T.APPLICATION_JSON))", 
                ClassName.get("org.springframework.test.web.servlet.result", "MockMvcResultMatchers"),
                ClassName.get("org.springframework.http", "MediaType"));
        
        if (serviceFieldName != null) {
            testMethod.addStatement("$T.verify($L).findAll()", 
                ClassName.get("org.mockito", "Mockito"), serviceFieldName);
        }
        
        testClass.addMethod(testMethod.build());
    }
    
    private void addBasicTestMethods(TypeSpec.Builder testClass, GeneratedClass controller, GeneratedClass service) {
        // Add a basic test method for controllers without clear entity mapping
        MethodSpec.Builder testMethod = MethodSpec.methodBuilder("testBasicEndpoint")
            .addModifiers(Modifier.PUBLIC)
            .addAnnotation(ClassName.get("org.junit.jupiter.api", "Test"))
            .addException(Exception.class)
            .addStatement("// Test basic endpoint functionality")
            .addStatement("mockMvc.perform($T.get($S))", 
                ClassName.get("org.springframework.test.web.servlet.request", "MockMvcRequestBuilders"),
                extractControllerPath(controller.getClassName()))
            .addStatement("    .andExpect($T.status().isOk())", 
                ClassName.get("org.springframework.test.web.servlet.result", "MockMvcResultMatchers"));
        
        testClass.addMethod(testMethod.build());
    }
    
    private Optional<GeneratedClass> findCorrespondingService(GeneratedClass controller, List<GeneratedClass> services) {
        String controllerName = controller.getClassName().replace("Controller", "");
        return services.stream()
            .filter(s -> s.getClassName().toLowerCase().contains(controllerName.toLowerCase()))
            .findFirst();
    }
    
    private Optional<GeneratedClass> findCorrespondingEntity(GeneratedClass controller, List<GeneratedClass> entities) {
        String controllerName = controller.getClassName().replace("Controller", "");
        return entities.stream()
            .filter(e -> e.getClassName().toLowerCase().contains(controllerName.toLowerCase()))
            .findFirst();
    }
    
    private String getServiceFieldName(String serviceClassName) {
        return Character.toLowerCase(serviceClassName.charAt(0)) + serviceClassName.substring(1);
    }
    
    private void addGetEntityByIdTest(TypeSpec.Builder testClass, String entityName,
                                     String entityVarName, String serviceFieldName, String controllerPath) {
        MethodSpec.Builder testMethod = MethodSpec.methodBuilder("testGet" + entityName + "ById")
            .addModifiers(Modifier.PUBLIC)
            .addAnnotation(ClassName.get("org.junit.jupiter.api", "Test"))
            .addException(Exception.class)
            .addStatement("// Given")
            .addStatement("$T id = 1L", Long.class)
            .addStatement("$T $L = new $T()",
                ClassName.get(config.getBasePackage() + ".entity", entityName),
                entityVarName,
                ClassName.get(config.getBasePackage() + ".entity", entityName));

        if (serviceFieldName != null) {
            testMethod.addStatement("$T.when($L.findById(id)).thenReturn($T.of($L))",
                ClassName.get("org.mockito", "Mockito"), serviceFieldName,
                ClassName.get(Optional.class), entityVarName);
        }

        testMethod.addStatement("")
            .addStatement("// When & Then")
            .addStatement("mockMvc.perform($T.get($S + \"/\" + id))",
                ClassName.get("org.springframework.test.web.servlet.request", "MockMvcRequestBuilders"),
                controllerPath)
            .addStatement("    .andExpect($T.status().isOk())",
                ClassName.get("org.springframework.test.web.servlet.result", "MockMvcResultMatchers"))
            .addStatement("    .andExpect($T.content().contentType($T.APPLICATION_JSON))",
                ClassName.get("org.springframework.test.web.servlet.result", "MockMvcResultMatchers"),
                ClassName.get("org.springframework.http", "MediaType"));

        if (serviceFieldName != null) {
            testMethod.addStatement("$T.verify($L).findById(id)",
                ClassName.get("org.mockito", "Mockito"), serviceFieldName);
        }

        testClass.addMethod(testMethod.build());
    }

    private void addCreateEntityTest(TypeSpec.Builder testClass, String entityName,
                                   String entityVarName, String serviceFieldName,
                                   String controllerPath, GeneratedClass entity) {
        MethodSpec.Builder testMethod = MethodSpec.methodBuilder("testCreate" + entityName)
            .addModifiers(Modifier.PUBLIC)
            .addAnnotation(ClassName.get("org.junit.jupiter.api", "Test"))
            .addException(Exception.class)
            .addStatement("// Given")
            .addStatement("$T $L = new $T()",
                ClassName.get(config.getBasePackage() + ".entity", entityName),
                entityVarName,
                ClassName.get(config.getBasePackage() + ".entity", entityName))
            .addStatement("$T saved$L = new $T()",
                ClassName.get(config.getBasePackage() + ".entity", entityName),
                entityName,
                ClassName.get(config.getBasePackage() + ".entity", entityName));

        if (serviceFieldName != null) {
            testMethod.addStatement("$T.when($L.save($T.any($T.class))).thenReturn(saved$L)",
                ClassName.get("org.mockito", "Mockito"), serviceFieldName,
                ClassName.get("org.mockito", "ArgumentMatchers"),
                ClassName.get(config.getBasePackage() + ".entity", entityName),
                entityName);
        }

        testMethod.addStatement("")
            .addStatement("// When & Then")
            .addStatement("mockMvc.perform($T.post($S)",
                ClassName.get("org.springframework.test.web.servlet.request", "MockMvcRequestBuilders"),
                controllerPath)
            .addStatement("        .contentType($T.APPLICATION_JSON)",
                ClassName.get("org.springframework.http", "MediaType"))
            .addStatement("        .content(objectMapper.writeValueAsString($L)))", entityVarName)
            .addStatement("    .andExpect($T.status().isCreated())",
                ClassName.get("org.springframework.test.web.servlet.result", "MockMvcResultMatchers"))
            .addStatement("    .andExpect($T.content().contentType($T.APPLICATION_JSON))",
                ClassName.get("org.springframework.test.web.servlet.result", "MockMvcResultMatchers"),
                ClassName.get("org.springframework.http", "MediaType"));

        if (serviceFieldName != null) {
            testMethod.addStatement("$T.verify($L).save($T.any($T.class))",
                ClassName.get("org.mockito", "Mockito"), serviceFieldName,
                ClassName.get("org.mockito", "ArgumentMatchers"),
                ClassName.get(config.getBasePackage() + ".entity", entityName));
        }

        testClass.addMethod(testMethod.build());
    }

    private void addUpdateEntityTest(TypeSpec.Builder testClass, String entityName,
                                   String entityVarName, String serviceFieldName,
                                   String controllerPath, GeneratedClass entity) {
        MethodSpec.Builder testMethod = MethodSpec.methodBuilder("testUpdate" + entityName)
            .addModifiers(Modifier.PUBLIC)
            .addAnnotation(ClassName.get("org.junit.jupiter.api", "Test"))
            .addException(Exception.class)
            .addStatement("// Given")
            .addStatement("$T id = 1L", Long.class)
            .addStatement("$T $L = new $T()",
                ClassName.get(config.getBasePackage() + ".entity", entityName),
                entityVarName,
                ClassName.get(config.getBasePackage() + ".entity", entityName))
            .addStatement("$T updated$L = new $T()",
                ClassName.get(config.getBasePackage() + ".entity", entityName),
                entityName,
                ClassName.get(config.getBasePackage() + ".entity", entityName));

        if (serviceFieldName != null) {
            testMethod.addStatement("$T.when($L.update(id, $L)).thenReturn(updated$L)",
                ClassName.get("org.mockito", "Mockito"), serviceFieldName, entityVarName, entityName);
        }

        testMethod.addStatement("")
            .addStatement("// When & Then")
            .addStatement("mockMvc.perform($T.put($S + \"/\" + id)",
                ClassName.get("org.springframework.test.web.servlet.request", "MockMvcRequestBuilders"),
                controllerPath)
            .addStatement("        .contentType($T.APPLICATION_JSON)",
                ClassName.get("org.springframework.http", "MediaType"))
            .addStatement("        .content(objectMapper.writeValueAsString($L)))", entityVarName)
            .addStatement("    .andExpect($T.status().isOk())",
                ClassName.get("org.springframework.test.web.servlet.result", "MockMvcResultMatchers"))
            .addStatement("    .andExpect($T.content().contentType($T.APPLICATION_JSON))",
                ClassName.get("org.springframework.test.web.servlet.result", "MockMvcResultMatchers"),
                ClassName.get("org.springframework.http", "MediaType"));

        if (serviceFieldName != null) {
            testMethod.addStatement("$T.verify($L).update(id, $L)",
                ClassName.get("org.mockito", "Mockito"), serviceFieldName, entityVarName);
        }

        testClass.addMethod(testMethod.build());
    }

    private void addDeleteEntityTest(TypeSpec.Builder testClass, String entityName,
                                   String entityVarName, String serviceFieldName, String controllerPath) {
        MethodSpec.Builder testMethod = MethodSpec.methodBuilder("testDelete" + entityName)
            .addModifiers(Modifier.PUBLIC)
            .addAnnotation(ClassName.get("org.junit.jupiter.api", "Test"))
            .addException(Exception.class)
            .addStatement("// Given")
            .addStatement("$T id = 1L", Long.class)
            .addStatement("")
            .addStatement("// When & Then")
            .addStatement("mockMvc.perform($T.delete($S + \"/\" + id))",
                ClassName.get("org.springframework.test.web.servlet.request", "MockMvcRequestBuilders"),
                controllerPath)
            .addStatement("    .andExpect($T.status().isNoContent())",
                ClassName.get("org.springframework.test.web.servlet.result", "MockMvcResultMatchers"));

        if (serviceFieldName != null) {
            testMethod.addStatement("$T.verify($L).deleteById(id)",
                ClassName.get("org.mockito", "Mockito"), serviceFieldName);
        }

        testClass.addMethod(testMethod.build());
    }

    private String extractControllerPath(String controllerClassName) {
        String entityName = controllerClassName.replace("Controller", "").toLowerCase();
        return config.getApiPrefix() + "/" + entityName + "s";
    }
}
