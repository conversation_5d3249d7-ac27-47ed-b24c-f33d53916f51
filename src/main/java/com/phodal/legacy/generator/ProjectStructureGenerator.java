package com.phodal.legacy.generator;

import com.phodal.legacy.services.AnalysisService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardOpenOption;

/**
 * Generates Spring Boot project structure files like build.gradle, application.properties, etc.
 */
public class ProjectStructureGenerator {
    
    private static final Logger logger = LoggerFactory.getLogger(ProjectStructureGenerator.class);
    
    private final GenerationConfig config;
    
    public ProjectStructureGenerator(GenerationConfig config) {
        this.config = config;
    }
    
    /**
     * Generate complete Spring Boot project structure
     */
    public void generateProjectStructure(Path targetPath, AnalysisService.AnalysisResult analysisResult) throws IOException {
        generateBuildGradle(targetPath);
        generateSettingsGradle(targetPath);
        generateGradleWrapper(targetPath);
        generateApplicationProperties(targetPath);
        generateApplicationYml(targetPath);
        generateGitignore(targetPath);
        generateReadme(targetPath);

        logger.info("Generated Spring Boot project structure files");
    }
    
    private void generateBuildGradle(Path targetPath) throws IOException {
        String buildGradleContent = generateBuildGradleContent();
        Path buildGradlePath = targetPath.resolve("build.gradle");
        Files.write(buildGradlePath, buildGradleContent.getBytes(), StandardOpenOption.CREATE, StandardOpenOption.TRUNCATE_EXISTING);
        logger.info("Generated build.gradle");
    }
    
    private String generateBuildGradleContent() {
        return String.format("""
            plugins {
                id 'java'
                id 'org.springframework.boot' version '%s'
                id 'io.spring.dependency-management' version '1.1.4'
            }
            
            group = '%s'
            version = '0.0.1-SNAPSHOT'
            
            java {
                sourceCompatibility = '%s'
                targetCompatibility = '%s'
            }
            
            repositories {
                mavenCentral()
            }
            
            dependencies {
                implementation 'org.springframework.boot:spring-boot-starter-web'
                implementation 'org.springframework.boot:spring-boot-starter-data-jpa'
                implementation 'org.springframework.boot:spring-boot-starter-validation'
                
                // Database
                runtimeOnly 'com.h2database:h2'
                // Uncomment for MySQL
                // runtimeOnly 'mysql:mysql-connector-java'
                // Uncomment for PostgreSQL
                // runtimeOnly 'org.postgresql:postgresql'
                
                // Development tools
                developmentOnly 'org.springframework.boot:spring-boot-devtools'
                
                // Testing
                testImplementation 'org.springframework.boot:spring-boot-starter-test'
                testImplementation 'org.springframework.boot:spring-boot-testcontainers'
                testImplementation 'org.testcontainers:junit-jupiter'
                testImplementation 'org.testcontainers:h2'

                // Additional testing dependencies for generated tests
                testImplementation 'org.junit.jupiter:junit-jupiter:5.10.1'
                testImplementation 'org.mockito:mockito-core:5.8.0'
                testImplementation 'org.mockito:mockito-junit-jupiter:5.8.0'
                testImplementation 'org.assertj:assertj-core:3.25.1'
                testImplementation 'com.fasterxml.jackson.core:jackson-databind'
            }
            
            tasks.named('test') {
                useJUnitPlatform()
            }
            """, 
            config.getSpringBootVersion(),
            config.getBasePackage(),
            config.getJavaVersion(),
            config.getJavaVersion());
    }
    
    private void generateApplicationProperties(Path targetPath) throws IOException {
        String propertiesContent = generateApplicationPropertiesContent();
        Path propertiesPath = targetPath.resolve("src/main/resources/application.properties");
        Files.createDirectories(propertiesPath.getParent());
        Files.write(propertiesPath, propertiesContent.getBytes(), StandardOpenOption.CREATE, StandardOpenOption.TRUNCATE_EXISTING);
        logger.info("Generated application.properties");
    }
    
    private String generateApplicationPropertiesContent() {
        return String.format("""
            # Application Configuration
            spring.application.name=migrated-app
            
            # Server Configuration
            server.port=8080
            server.servlet.context-path=%s
            
            # Database Configuration (H2 for development)
            spring.datasource.url=jdbc:h2:mem:testdb
            spring.datasource.driverClassName=org.h2.Driver
            spring.datasource.username=sa
            spring.datasource.password=password
            
            # JPA Configuration
            spring.jpa.database-platform=org.hibernate.dialect.H2Dialect
            spring.jpa.hibernate.ddl-auto=create-drop
            spring.jpa.show-sql=true
            spring.jpa.properties.hibernate.format_sql=true
            
            # H2 Console (for development only)
            spring.h2.console.enabled=true
            spring.h2.console.path=/h2-console
            
            # Logging Configuration
            logging.level.%s=DEBUG
            logging.level.org.springframework.web=DEBUG
            logging.level.org.hibernate.SQL=DEBUG
            logging.level.org.hibernate.type.descriptor.sql.BasicBinder=TRACE
            
            # Actuator Configuration
            management.endpoints.web.exposure.include=health,info,metrics
            management.endpoint.health.show-details=when-authorized
            """,
            config.getApiPrefix(),
            config.getBasePackage());
    }
    
    private void generateApplicationYml(Path targetPath) throws IOException {
        String ymlContent = generateApplicationYmlContent();
        Path ymlPath = targetPath.resolve("src/main/resources/application.yml");
        Files.write(ymlPath, ymlContent.getBytes(), StandardOpenOption.CREATE, StandardOpenOption.TRUNCATE_EXISTING);
        logger.info("Generated application.yml");
    }
    
    private String generateApplicationYmlContent() {
        return String.format("""
            spring:
              application:
                name: migrated-app
              
              profiles:
                active: development
              
              datasource:
                url: jdbc:h2:mem:testdb
                driver-class-name: org.h2.Driver
                username: sa
                password: password
              
              jpa:
                database-platform: org.hibernate.dialect.H2Dialect
                hibernate:
                  ddl-auto: create-drop
                show-sql: true
                properties:
                  hibernate:
                    format_sql: true
              
              h2:
                console:
                  enabled: true
                  path: /h2-console
            
            server:
              port: 8080
              servlet:
                context-path: %s
            
            logging:
              level:
                %s: DEBUG
                org.springframework.web: DEBUG
                org.hibernate.SQL: DEBUG
                org.hibernate.type.descriptor.sql.BasicBinder: TRACE
            
            management:
              endpoints:
                web:
                  exposure:
                    include: health,info,metrics
              endpoint:
                health:
                  show-details: when-authorized
            
            ---
            spring:
              config:
                activate:
                  on-profile: production
              
              datasource:
                url: ****************************************
                driver-class-name: com.mysql.cj.jdbc.Driver
                username: ${DB_USERNAME:app_user}
                password: ${DB_PASSWORD:app_password}
              
              jpa:
                hibernate:
                  ddl-auto: validate
                show-sql: false
            
            logging:
              level:
                %s: INFO
                org.springframework.web: WARN
                org.hibernate.SQL: WARN
            """,
            config.getApiPrefix(),
            config.getBasePackage(),
            config.getBasePackage());
    }
    
    private void generateGitignore(Path targetPath) throws IOException {
        String gitignoreContent = """
            HELP.md
            .gradle
            build/
            !gradle/wrapper/gradle-wrapper.jar
            !**/src/main/**/build/
            !**/src/test/**/build/
            
            ### STS ###
            .apt_generated
            .classpath
            .factorypath
            .project
            .settings
            .springBeans
            .sts4-cache
            bin/
            !**/src/main/**/bin/
            !**/src/test/**/bin/
            
            ### IntelliJ IDEA ###
            .idea
            *.iws
            *.iml
            *.ipr
            out/
            !**/src/main/**/out/
            !**/src/test/**/out/
            
            ### NetBeans ###
            /nbproject/private/
            /nbbuild/
            /dist/
            /nbdist/
            /.nb-gradle/
            
            ### VS Code ###
            .vscode/
            
            ### Mac ###
            .DS_Store
            
            ### Logs ###
            *.log
            logs/
            
            ### Database ###
            *.db
            *.sqlite
            """;
        
        Path gitignorePath = targetPath.resolve(".gitignore");
        Files.write(gitignorePath, gitignoreContent.getBytes(), StandardOpenOption.CREATE, StandardOpenOption.TRUNCATE_EXISTING);
        logger.info("Generated .gitignore");
    }
    
    private void generateReadme(Path targetPath) throws IOException {
        String readmeContent = String.format("""
            # Migrated Spring Boot Application
            
            This application was automatically generated from a legacy JSP project using the JSP to Spring Boot migration tool.
            
            ## Getting Started
            
            ### Prerequisites
            - Java %s or higher
            - Gradle 7.0 or higher
            
            ### Running the Application
            
            1. Clone this repository
            2. Navigate to the project directory
            3. Run the application:
               ```bash
               ./gradlew bootRun
               ```
            
            The application will start on `http://localhost:8080%s`
            
            ### API Endpoints
            
            The application provides RESTful API endpoints under `%s`:
            
            - `GET %s/posts` - Get all posts
            - `GET %s/posts/{id}` - Get a specific post
            - `POST %s/posts` - Create a new post
            - `PUT %s/posts/{id}` - Update a post
            - `DELETE %s/posts/{id}` - Delete a post
            
            ### Database
            
            The application uses H2 in-memory database for development. You can access the H2 console at:
            `http://localhost:8080/h2-console`
            
            - JDBC URL: `jdbc:h2:mem:testdb`
            - Username: `sa`
            - Password: `password`
            
            ### Testing
            
            Run tests with:
            ```bash
            ./gradlew test
            ```
            
            ### Building for Production
            
            Build the application:
            ```bash
            ./gradlew build
            ```
            
            The JAR file will be created in `build/libs/`
            
            ## Migration Notes
            
            This application was migrated from a JSP-based application. Some manual adjustments may be needed:
            
            1. Review and update business logic in service classes
            2. Adjust database schema and entity relationships
            3. Update API endpoints to match your requirements
            4. Configure production database settings
            5. Add authentication and authorization if needed
            
            ## Technology Stack
            
            - Spring Boot %s
            - Spring Data JPA
            - H2 Database (development)
            - Gradle
            - Java %s
            """,
            config.getJavaVersion(),
            config.getApiPrefix(),
            config.getApiPrefix(),
            config.getApiPrefix(),
            config.getApiPrefix(),
            config.getApiPrefix(),
            config.getApiPrefix(),
            config.getApiPrefix(),
            config.getSpringBootVersion(),
            config.getJavaVersion());
        
        Path readmePath = targetPath.resolve("README.md");
        Files.write(readmePath, readmeContent.getBytes(), StandardOpenOption.CREATE, StandardOpenOption.TRUNCATE_EXISTING);
        logger.info("Generated README.md");
    }

    private void generateSettingsGradle(Path targetPath) throws IOException {
        String settingsContent = """
            rootProject.name = 'migrated-app'
            """;

        Path settingsPath = targetPath.resolve("settings.gradle");
        Files.write(settingsPath, settingsContent.getBytes(), StandardOpenOption.CREATE, StandardOpenOption.TRUNCATE_EXISTING);
        logger.info("Generated settings.gradle");
    }

    private void generateGradleWrapper(Path targetPath) throws IOException {
        // Create gradle wrapper directory
        Path gradleWrapperDir = targetPath.resolve("gradle/wrapper");
        Files.createDirectories(gradleWrapperDir);

        // Generate gradle-wrapper.properties
        String wrapperPropertiesContent = """
            distributionBase=GRADLE_USER_HOME
            distributionPath=wrapper/dists
            distributionUrl=https\\://services.gradle.org/distributions/gradle-8.5-bin.zip
            networkTimeout=10000
            validateDistributionUrl=true
            zipStoreBase=GRADLE_USER_HOME
            zipStorePath=wrapper/dists
            """;

        Path wrapperPropertiesPath = gradleWrapperDir.resolve("gradle-wrapper.properties");
        Files.write(wrapperPropertiesPath, wrapperPropertiesContent.getBytes(),
            StandardOpenOption.CREATE, StandardOpenOption.TRUNCATE_EXISTING);

        // Generate gradlew script (Unix)
        String gradlewContent = """
            #!/bin/sh

            #
            # Copyright © 2015-2021 the original authors.
            #
            # Licensed under the Apache License, Version 2.0 (the "License");
            # you may not use this file except in compliance with the License.
            # You may obtain a copy of the License at
            #
            #      https://www.apache.org/licenses/LICENSE-2.0
            #
            # Unless required by applicable law or agreed to in writing, software
            # distributed under the License is distributed on an "AS IS" BASIS,
            # WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
            # See the License for the specific language governing permissions and
            # limitations under the License.
            #

            ##############################################################################
            #
            #   Gradle start up script for POSIX generated by Gradle.
            #
            #   Important for running:
            #
            #   (1) You need a POSIX-compliant shell to run this script. If your /bin/sh is
            #       noncompliant, but you have some other compliant shell such as ksh or
            #       bash, then to run this script, type that shell name before the whole
            #       command line, like:
            #
            #           ksh Gradle
            #
            #       Busybox and similar reduced shells will NOT work, because this script
            #       requires all of these POSIX shell features:
            #         * functions;
            #         * expansions «$var», «${var}», «${var:-default}», «${var+SET}»,
            #           «${var#prefix}», «${var%suffix}», and «$( cmd )»;
            #         * compound commands having a testable exit status, especially «case»;
            #         * various built-in commands including «command», «set», and «ulimit».
            #
            #   Important for patching:
            #
            #   (2) This script targets any POSIX shell, so it avoids extensions provided
            #       by Bash, Ksh, etc; in particular arrays are avoided.
            #
            #       The "traditional" practice of packing multiple parameters into a
            #       space-separated string is a well documented source of bugs and security
            #       problems, so this is (mostly) avoided, by progressively accumulating
            #       options in "$@", and eventually passing that to Java.
            #
            #       Where the inherited environment variables (DEFAULT_JVM_OPTS, JAVA_OPTS,
            #       and GRADLE_OPTS) rely on word-splitting, this is performed explicitly;
            #       see the in-line comments for details.
            #
            #       There are tweaks for specific operating systems such as AIX, CygWin,
            #       Darwin, MinGW, and NonStop.
            #
            #   (3) This script is generated from the Gradle template within the Gradle project.
            #
            #       You can find Gradle at https://github.com/gradle/gradle/.
            #
            ##############################################################################

            # Attempt to set APP_HOME

            # Resolve links: $0 may be a link
            app_path=$0

            # Need this for daisy-chained symlinks.
            while
                APP_HOME=${app_path%"${app_path##*/}"}  # leaves a trailing /; empty if no leading path
                [ -h "$app_path" ]
            do
                ls=$( ls -ld "$app_path" )
                link=${ls#*' -> '}
                case $link in             #(
                  /*)   app_path=$link ;; #(
                  *)    app_path=$APP_HOME$link ;;
                esac
            done

            # This is normally unused
            # shellcheck disable=SC2034
            APP_BASE_NAME=${0##*/}
            # Discard cd standard output in case $CDPATH is set (https://github.com/gradle/gradle/issues/25036)
            APP_HOME=$( cd "${APP_HOME:-./}" > /dev/null && pwd -P ) || exit

            # Use the maximum available, or set MAX_FD != -1 to use that value.
            MAX_FD=maximum

            warn () {
                echo "$*"
            } >&2

            die () {
                echo
                echo "$*"
                echo
                exit 1
            } >&2

            # OS specific support (must be 'true' or 'false').
            cygwin=false
            msys=false
            darwin=false
            nonstop=false
            case "$( uname )" in                #(
              CYGWIN* )         cygwin=true  ;; #(
              Darwin* )         darwin=true  ;; #(
              MSYS* | MINGW* )  msys=true    ;; #(
              NONSTOP* )        nonstop=true ;;
            esac

            CLASSPATH=$APP_HOME/gradle/wrapper/gradle-wrapper.jar


            # Determine the Java command to use to start the JVM.
            if [ -n "$JAVA_HOME" ] ; then
                if [ -x "$JAVA_HOME/jre/sh/java" ] ; then
                    # IBM's JDK on AIX uses strange locations for the executables
                    JAVACMD=$JAVA_HOME/jre/sh/java
                else
                    JAVACMD=$JAVA_HOME/bin/java
                fi
                if [ ! -x "$JAVACMD" ] ; then
                    die "ERROR: JAVA_HOME is set to an invalid directory: $JAVA_HOME

            Please set the JAVA_HOME variable in your environment to match the
            location of your Java installation."
                fi
            else
                JAVACMD=java
                if ! command -v java >/dev/null 2>&1
                then
                    die "ERROR: JAVA_HOME is not set and no 'java' command could be found in your PATH.

            Please set the JAVA_HOME variable in your environment to match the
            location of your Java installation."
                fi
            fi

            # Increase the maximum file descriptors if we can.
            if ! "$cygwin" && ! "$darwin" && ! "$nonstop" ; then
                case $MAX_FD in #(
                  max*)
                    # In POSIX sh, ulimit -H is undefined. That's why the result is checked to see if it worked.
                    # shellcheck disable=SC2039,SC3045
                    MAX_FD=$( ulimit -H -n ) ||
                        warn "Could not query maximum file descriptor limit"
                esac
                case $MAX_FD in  #(
                  '' | soft) :;; #(
                  *)
                    # In POSIX sh, ulimit -n is undefined. That's why the result is checked to see if it worked.
                    # shellcheck disable=SC2039,SC3045
                    ulimit -n "$MAX_FD" ||
                        warn "Could not set maximum file descriptor limit to $MAX_FD"
                esac
            fi

            # Collect all arguments for the java command, stacking in reverse order:
            #   * args from the command line
            #   * the main class name
            #   * -classpath
            #   * -D...appname settings
            #   * --module-path (only if needed)
            #   * DEFAULT_JVM_OPTS, JAVA_OPTS, and GRADLE_OPTS environment variables.

            # For Cygwin or MSYS, switch paths to Windows format before running java
            if "$cygwin" || "$msys" ; then
                APP_HOME=$( cygpath --path --mixed "$APP_HOME" )
                CLASSPATH=$( cygpath --path --mixed "$CLASSPATH" )

                JAVACMD=$( cygpath --unix "$JAVACMD" )

                # Now convert the arguments - kludge to limit ourselves to /bin/sh
                for arg do
                    if
                        case $arg in                                #(
                          -*)   false ;;                            # don't mess with options #(
                          /?*)  t=${arg#/} t=/${t%%/*}              # looks like a POSIX filepath
                                [ -e "$t" ] ;;                      #(
                          *)    false ;;
                        esac
                    then
                        arg=$( cygpath --path --ignore --mixed "$arg" )
                    fi
                    # Roll the args list around exactly as many times as the number of
                    # args, so each arg winds up back in the position where it started, but
                    # possibly modified.
                    #
                    # NB: a `for` loop captures its iteration list before it begins, so
                    # changing the positional parameters here affects neither the number of
                    # iterations, nor the values presented in `arg`.
                    shift                   # remove old arg
                    set -- "$@" "$arg"      # push replacement arg
                done
            fi


            # Add default JVM options here. You can also use JAVA_OPTS and GRADLE_OPTS to pass JVM options to this script.
            DEFAULT_JVM_OPTS='"-Xmx64m" "-Xms64m"'

            # Collect all arguments for the java command:
            #   * DEFAULT_JVM_OPTS, JAVA_OPTS, JAVA_OPTS, and optsEnvironmentVar are not allowed to contain shell fragments,
            #     and any embedded shellness will be escaped.
            #   * For example: A user cannot expect ${Hostname} to be expanded, as it is an environment variable and will be
            #     treated as '${Hostname}' itself on the command line.

            set -- \\
                    "-Dorg.gradle.appname=$APP_BASE_NAME" \\
                    -classpath "$CLASSPATH" \\
                    org.gradle.wrapper.GradleWrapperMain \\
                    "$@"

            # Stop when "xargs" is not available.
            if ! command -v xargs >/dev/null 2>&1
            then
                die "xargs is not available"
            fi

            # Use "xargs" to parse quoted args.
            #
            # With -n1 it outputs one arg per line, with the quotes and backslashes removed.
            #
            # In Bash we could simply go:
            #
            #   readarray ARGS < <( xargs -n1 <<<"$var" ) &&
            #   set -- "${ARGS[@]}" "$@"
            #
            # but POSIX shell has neither arrays nor command substitution, so instead we
            # post-process each arg (as a line of input to sed) to backslash-escape any
            # character that might be a shell metacharacter, then use eval to reverse
            # that process (while maintaining the separation between arguments).
            #
            # This will of course break if any of these variables contains a newline or
            # an unmatched quote.
            #

            eval "set -- $(
                    printf '%s\\n' "$DEFAULT_JVM_OPTS $JAVA_OPTS $GRADLE_OPTS" |
                    xargs -n1 |
                    sed ' s~[^-[:alnum:]+,./:=@_]~\\&~g; ' |
                    tr '\\n' ' '
                )" '"$@"'

            exec "$JAVACMD" "$@"
            """;

        Path gradlewPath = targetPath.resolve("gradlew");
        Files.write(gradlewPath, gradlewContent.getBytes(),
            StandardOpenOption.CREATE, StandardOpenOption.TRUNCATE_EXISTING);

        // Make gradlew executable
        gradlewPath.toFile().setExecutable(true);

        logger.info("Generated gradle wrapper files");
    }
}
