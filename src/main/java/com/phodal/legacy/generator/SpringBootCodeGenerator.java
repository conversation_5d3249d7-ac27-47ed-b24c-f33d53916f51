package com.phodal.legacy.generator;

import com.phodal.legacy.model.ComponentModel;
import com.phodal.legacy.model.DependencyGraph;
import com.phodal.legacy.services.AnalysisService;
import com.phodal.legacy.utils.LoggingUtils;
import com.squareup.javapoet.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.lang.model.element.Modifier;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Main code generator for converting JSP projects to Spring Boot applications.
 * 
 * This generator orchestrates the conversion process by:
 * 1. Analyzing the source JSP project structure
 * 2. Generating Spring Boot project structure
 * 3. Converting JSP pages to REST controllers
 * 4. Creating entity and repository classes
 * 5. Generating service layer classes
 * 6. Creating configuration files
 */
public class SpringBootCodeGenerator {
    
    private static final Logger logger = LoggerFactory.getLogger(SpringBootCodeGenerator.class);
    
    private final GenerationConfig config;
    private final RestControllerGenerator restControllerGenerator;
    private final EntityRepositoryGenerator entityRepositoryGenerator;
    private final ServiceLayerGenerator serviceLayerGenerator;
    private final ProjectStructureGenerator projectStructureGenerator;
    
    public SpringBootCodeGenerator(GenerationConfig config) {
        this.config = config;
        this.restControllerGenerator = new RestControllerGenerator(config);
        this.entityRepositoryGenerator = new EntityRepositoryGenerator(config);
        this.serviceLayerGenerator = new ServiceLayerGenerator(config);
        this.projectStructureGenerator = new ProjectStructureGenerator(config);
    }
    
    /**
     * Main method to convert a JSP project to Spring Boot application
     */
    public ConversionResult convertProject(Path sourcePath, Path targetPath, 
                                         AnalysisService.AnalysisResult analysisResult) {
        LoggingUtils.logOperationStart("convertProject", "SpringBootCodeGenerator");
        
        try {
            // Create target directory structure
            createTargetDirectoryStructure(targetPath);
            
            // Generate project structure files (build.gradle, application.properties, etc.)
            projectStructureGenerator.generateProjectStructure(targetPath, analysisResult);
            
            // Extract components from analysis result
            List<ComponentModel> jspComponents = extractJspComponents(analysisResult);
            List<ComponentModel> javaComponents = extractJavaComponents(analysisResult);
            List<ComponentModel> servletComponents = extractServletComponents(analysisResult);
            
            // Generate entities and repositories
            List<GeneratedClass> entities = entityRepositoryGenerator.generateEntities(javaComponents, targetPath);
            List<GeneratedClass> repositories = entityRepositoryGenerator.generateRepositories(entities, targetPath);
            
            // Generate service layer
            List<GeneratedClass> services = serviceLayerGenerator.generateServices(javaComponents, entities, targetPath);
            
            // Generate REST controllers from JSP pages and servlets
            List<GeneratedClass> controllers = restControllerGenerator.generateControllers(
                jspComponents, servletComponents, services, targetPath);
            
            // Generate main application class
            GeneratedClass mainApp = generateMainApplicationClass(targetPath);
            
            ConversionResult result = new ConversionResult();
            result.setSuccess(true);
            result.setTargetPath(targetPath);
            result.addGeneratedClasses(entities);
            result.addGeneratedClasses(repositories);
            result.addGeneratedClasses(services);
            result.addGeneratedClasses(controllers);
            result.addGeneratedClass(mainApp);
            
            LoggingUtils.logOperationComplete("convertProject", "SpringBootCodeGenerator");
            logger.info("Successfully converted JSP project to Spring Boot application");
            logger.info("Generated {} classes in total", result.getGeneratedClasses().size());
            
            return result;
            
        } catch (Exception e) {
            logger.error("Failed to convert JSP project: {}", e.getMessage(), e);
            ConversionResult result = new ConversionResult();
            result.setSuccess(false);
            result.setErrorMessage(e.getMessage());
            return result;
        }
    }
    
    private void createTargetDirectoryStructure(Path targetPath) throws IOException {
        // Create main directory structure
        Files.createDirectories(targetPath);
        Files.createDirectories(targetPath.resolve("src/main/java"));
        Files.createDirectories(targetPath.resolve("src/main/resources"));
        Files.createDirectories(targetPath.resolve("src/test/java"));
        
        // Create package directories
        String packagePath = config.getBasePackage().replace(".", "/");
        Files.createDirectories(targetPath.resolve("src/main/java").resolve(packagePath));
        Files.createDirectories(targetPath.resolve("src/main/java").resolve(packagePath + "/controller"));
        Files.createDirectories(targetPath.resolve("src/main/java").resolve(packagePath + "/service"));
        Files.createDirectories(targetPath.resolve("src/main/java").resolve(packagePath + "/repository"));
        Files.createDirectories(targetPath.resolve("src/main/java").resolve(packagePath + "/entity"));
        Files.createDirectories(targetPath.resolve("src/main/java").resolve(packagePath + "/config"));
        
        logger.info("Created Spring Boot project directory structure at: {}", targetPath);
    }
    
    private GeneratedClass generateMainApplicationClass(Path targetPath) throws IOException {
        String className = "Application";
        String packageName = config.getBasePackage();
        
        TypeSpec.Builder appClass = TypeSpec.classBuilder(className)
            .addModifiers(Modifier.PUBLIC)
            .addAnnotation(ClassName.get("org.springframework.boot.autoconfigure", "SpringBootApplication"))
            .addMethod(MethodSpec.methodBuilder("main")
                .addModifiers(Modifier.PUBLIC, Modifier.STATIC)
                .returns(void.class)
                .addParameter(String[].class, "args")
                .addStatement("$T.run($L.class, args)", 
                    ClassName.get("org.springframework.boot", "SpringApplication"), className)
                .build());
        
        JavaFile javaFile = JavaFile.builder(packageName, appClass.build())
            .addFileComment("Generated by JSP to Spring Boot migration tool")
            .build();
        
        Path javaPath = targetPath.resolve("src/main/java");
        javaFile.writeTo(javaPath);
        
        GeneratedClass generatedClass = new GeneratedClass();
        generatedClass.setClassName(className);
        generatedClass.setPackageName(packageName);
        generatedClass.setFilePath(javaPath.resolve(packageName.replace(".", "/")).resolve(className + ".java"));
        generatedClass.setType(GeneratedClass.ClassType.MAIN_APPLICATION);
        
        logger.info("Generated main application class: {}.{}", packageName, className);
        return generatedClass;
    }
    
    private List<ComponentModel> extractJspComponents(AnalysisService.AnalysisResult analysisResult) {
        return analysisResult.getJspComponents() != null ?
            analysisResult.getJspComponents() : new ArrayList<>();
    }

    private List<ComponentModel> extractJavaComponents(AnalysisService.AnalysisResult analysisResult) {
        return analysisResult.getJavaComponents() != null ?
            analysisResult.getJavaComponents() : new ArrayList<>();
    }

    private List<ComponentModel> extractServletComponents(AnalysisService.AnalysisResult analysisResult) {
        List<ComponentModel> servlets = new ArrayList<>();
        if (analysisResult.getJavaComponents() != null) {
            servlets.addAll(analysisResult.getJavaComponents().stream()
                .filter(c -> c.getType() == ComponentModel.ComponentType.SERVLET)
                .collect(Collectors.toList()));
        }
        return servlets;
    }

    private List<ComponentModel> getAllComponents(AnalysisService.AnalysisResult analysisResult) {
        List<ComponentModel> allComponents = new ArrayList<>();

        if (analysisResult.getJspComponents() != null) {
            allComponents.addAll(analysisResult.getJspComponents());
        }
        if (analysisResult.getJavaComponents() != null) {
            allComponents.addAll(analysisResult.getJavaComponents());
        }
        if (analysisResult.getBytecodeComponents() != null) {
            allComponents.addAll(analysisResult.getBytecodeComponents());
        }
        if (analysisResult.getWebXmlComponents() != null) {
            allComponents.addAll(analysisResult.getWebXmlComponents());
        }

        return allComponents;
    }
}
