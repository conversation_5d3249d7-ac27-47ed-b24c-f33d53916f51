plugins {
    id 'java'
    id 'application'
    id 'org.springframework.boot' version '3.2.1'
    id 'io.spring.dependency-management' version '1.1.4'
}

group = 'com.phodal.legacy'
version = '1.0.0'

java {
    sourceCompatibility = '17'
    targetCompatibility = '17'
}

repositories {
    mavenCentral()
    gradlePluginPortal()
}

dependencies {
    // CLI framework
    implementation 'info.picocli:picocli:4.7.5'
    // annotationProcessor 'info.picocli:picocli-codegen:4.7.5'  // Disabled due to compilation issues
    
    // Code generation
    implementation 'com.squareup:javapoet:1.13.0'
    implementation 'org.freemarker:freemarker:2.3.32'
    
    // Code parsing and analysis
    implementation 'com.github.javaparser:javaparser-core:3.25.7'
    implementation 'com.github.javaparser:javaparser-symbol-solver-core:3.25.7'
    implementation 'org.ow2.asm:asm:9.6'
    implementation 'org.ow2.asm:asm-tree:9.6'
    implementation 'org.ow2.asm:asm-analysis:9.6'
    implementation 'org.ow2.asm:asm-util:9.6'
    
    // JSP parsing
    implementation 'org.apache.tomcat:tomcat-jasper:10.1.17'
    implementation 'org.apache.tomcat:tomcat-jsp-api:10.1.17'
    
    // HTML processing
    implementation 'org.jsoup:jsoup:1.17.2'
    
    // XML processing for web.xml
    implementation 'org.dom4j:dom4j:2.1.4'
    implementation 'jaxen:jaxen:2.0.0'
    
    // OpenRewrite for code migration
    implementation 'org.openrewrite:rewrite-java:8.21.0'
    implementation 'org.openrewrite:rewrite-xml:8.21.0'
    implementation 'org.openrewrite:rewrite-gradle:8.21.0'
    
    // Spring Boot dependencies
    implementation 'org.springframework.boot:spring-boot-starter'
    implementation 'org.springframework.boot:spring-boot-starter-web'
    implementation 'org.springframework.boot:spring-boot-starter-logging'
    
    // Utilities
    implementation 'org.apache.commons:commons-lang3:3.14.0'
    implementation 'commons-io:commons-io:2.15.1'
    implementation 'com.google.guava:guava:32.1.3-jre'
    
    // JSON processing
    implementation 'com.fasterxml.jackson.core:jackson-databind:2.16.1'
    implementation 'com.fasterxml.jackson.dataformat:jackson-dataformat-yaml:2.16.1'
    
    // Logging
    implementation 'ch.qos.logback:logback-classic:1.4.14'
    implementation 'org.slf4j:slf4j-api:2.0.9'
    
    // Testing
    testImplementation 'org.springframework.boot:spring-boot-starter-test'
    testImplementation 'org.junit.jupiter:junit-jupiter:5.10.1'
    testImplementation 'org.mockito:mockito-core:5.8.0'
    testImplementation 'org.assertj:assertj-core:3.25.1'
}

application {
    mainClass = 'com.phodal.legacy.CliApp'
}

tasks.named('test') {
    useJUnitPlatform()
}

jar {
    manifest {
        attributes(
            'Main-Class': 'com.phodal.legacy.CliApp'
        )
    }
}

// Create fat jar for distribution
task fatJar(type: Jar) {
    archiveClassifier = 'all'
    duplicatesStrategy = DuplicatesStrategy.EXCLUDE
    manifest {
        attributes 'Main-Class': 'com.phodal.legacy.CliApp'
    }
    from {
        configurations.runtimeClasspath.collect { it.isDirectory() ? it : zipTree(it) }
    }
    with jar
}

// Configure compiler arguments for Picocli annotation processing
// Disabled due to compilation issues with annotation processor
// tasks.withType(JavaCompile) {
//     options.compilerArgs += ["-Aproject=${project.group}/${project.name}".toString()]
// }

// Configure Java compilation
compileJava {
    options.encoding = 'UTF-8'
}

compileTestJava {
    options.encoding = 'UTF-8'
}
